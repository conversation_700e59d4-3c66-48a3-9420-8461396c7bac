const axios = require('axios');
const https = require('https');
const Service = require('../models/Service');
const ProxmoxServer = require('../models/ProxmoxServer');

// Custom Proxmox Client Implementation
class ProxmoxClient {
  constructor(config) {
    this.host = config.host;
    this.port = config.port || 8006;
    this.username = config.username;
    this.password = config.password;
    this.realm = config.realm || 'pam';
    this.ticket = null;
    this.csrfToken = null;

    // Create axios instance with SSL verification disabled
    this.axios = axios.create({
      baseURL: `https://${this.host}:${this.port}/api2/json`,
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      }),
      timeout: 30000
    });
  }

  async authenticate() {
    try {
      const response = await this.axios.post('/access/ticket', {
        username: `${this.username}@${this.realm}`,
        password: this.password
      });

      this.ticket = response.data.data.ticket;
      this.csrfToken = response.data.data.CSRFPreventionToken;

      // Set default headers for authenticated requests
      this.axios.defaults.headers.common['Cookie'] = `PVEAuthCookie=${this.ticket}`;
      this.axios.defaults.headers.common['CSRFPreventionToken'] = this.csrfToken;

      return true;
    } catch (error) {
      console.error('Proxmox authentication failed:', error.message);
      throw new Error('Failed to authenticate with Proxmox');
    }
  }

  async request(method, path, data = null) {
    // Authenticate if no ticket
    if (!this.ticket) {
      await this.authenticate();
    }

    try {
      const config = {
        method,
        url: path,
        data
      };

      const response = await this.axios(config);
      return response.data.data;
    } catch (error) {
      // If authentication error, try to re-authenticate once
      if (error.response && error.response.status === 401) {
        this.ticket = null;
        await this.authenticate();

        const config = {
          method,
          url: path,
          data
        };

        const response = await this.axios(config);
        return response.data.data;
      }

      throw error;
    }
  }

  async get(path) {
    return this.request('GET', path);
  }

  async post(path, data) {
    return this.request('POST', path, data);
  }

  async put(path, data) {
    return this.request('PUT', path, data);
  }

  async delete(path) {
    return this.request('DELETE', path);
  }
}

class VPSControlService {
  constructor() {
    this.proxmoxClients = new Map();
  }

  async getProxmoxClient(serverId) {
    if (this.proxmoxClients.has(serverId)) {
      return this.proxmoxClients.get(serverId);
    }

    const server = await ProxmoxServer.findById(serverId);
    if (!server) {
      throw new Error('Proxmox server not found');
    }

    const client = new ProxmoxClient({
      host: server.host,
      port: server.port,
      username: server.user,
      password: server.password,
      realm: server.realm
    });

    this.proxmoxClients.set(serverId, client);
    return client;
  }

  async startVM(jobData) {
    const { serviceId, vmid, proxmoxServerId, node } = jobData;
    
    try {
      console.log(`🔄 Starting VM ${vmid} on node ${node}`);

      // Get service details
      const service = await Service.findById(serviceId);
      if (!service) {
        throw new Error('Service not found');
      }

      // Get Proxmox client
      const proxmox = await this.getProxmoxClient(proxmoxServerId);

      // Start VM/CT
      if (service.type === 'VM') {
        await proxmox.post(`/nodes/${node}/qemu/${vmid}/status/start`);
      } else {
        await proxmox.post(`/nodes/${node}/lxc/${vmid}/status/start`);
      }

      // Update service status
      service.proxmoxStatus = 'running';
      await service.save();

      console.log(`✅ VM ${vmid} started successfully`);

    } catch (error) {
      console.error(`❌ Failed to start VM ${vmid}:`, error);
      throw error;
    }
  }

  async stopVM(jobData) {
    const { serviceId, vmid, proxmoxServerId, node } = jobData;
    
    try {
      console.log(`🔄 Stopping VM ${vmid} on node ${node}`);

      // Get service details
      const service = await Service.findById(serviceId);
      if (!service) {
        throw new Error('Service not found');
      }

      // Get Proxmox client
      const proxmox = await this.getProxmoxClient(proxmoxServerId);

      // Stop VM/CT
      if (service.type === 'VM') {
        await proxmox.post(`/nodes/${node}/qemu/${vmid}/status/stop`);
      } else {
        await proxmox.post(`/nodes/${node}/lxc/${vmid}/status/stop`);
      }

      // Update service status
      service.proxmoxStatus = 'stopped';
      await service.save();

      console.log(`✅ VM ${vmid} stopped successfully`);

    } catch (error) {
      console.error(`❌ Failed to stop VM ${vmid}:`, error);
      throw error;
    }
  }

  async restartVM(jobData) {
    const { serviceId, vmid, proxmoxServerId, node } = jobData;
    
    try {
      console.log(`🔄 Restarting VM ${vmid} on node ${node}`);

      // Get service details
      const service = await Service.findById(serviceId);
      if (!service) {
        throw new Error('Service not found');
      }

      // Get Proxmox client
      const proxmox = await this.getProxmoxClient(proxmoxServerId);

      // Restart VM/CT
      if (service.type === 'VM') {
        await proxmox.post(`/nodes/${node}/qemu/${vmid}/status/reboot`);
      } else {
        await proxmox.post(`/nodes/${node}/lxc/${vmid}/status/reboot`);
      }

      // Update service status
      service.proxmoxStatus = 'running';
      await service.save();

      console.log(`✅ VM ${vmid} restarted successfully`);

    } catch (error) {
      console.error(`❌ Failed to restart VM ${vmid}:`, error);
      throw error;
    }
  }

  async reinstallVM(jobData) {
    const { serviceId, vmid, proxmoxServerId, node, osTemplate } = jobData;
    
    try {
      console.log(`🔄 Reinstalling VM ${vmid} with template ${osTemplate}`);

      // Get service details
      const service = await Service.findById(serviceId);
      if (!service) {
        throw new Error('Service not found');
      }

      // Get Proxmox client
      const proxmox = await this.getProxmoxClient(proxmoxServerId);

      if (service.type === 'VM') {
        // For VMs, we need to stop, delete, and recreate
        await this.reinstallVMInstance(proxmox, service, node, vmid, osTemplate);
      } else {
        // For CTs, we can restore from template
        await this.reinstallCTInstance(proxmox, service, node, vmid, osTemplate);
      }

      // Update service OS information
      service.os.template = osTemplate;
      await service.save();

      console.log(`✅ VM ${vmid} reinstalled successfully`);

    } catch (error) {
      console.error(`❌ Failed to reinstall VM ${vmid}:`, error);
      throw error;
    }
  }

  async reinstallVMInstance(proxmox, service, node, vmid, osTemplate) {
    // Stop VM if running
    try {
      await proxmox.post(`/nodes/${node}/qemu/${vmid}/status/stop`);
      // Wait for VM to stop
      await this.waitForVMStatus(proxmox, node, vmid, 'stopped', 'qemu');
    } catch (error) {
      // VM might already be stopped
      console.log(`VM ${vmid} already stopped or error stopping:`, error.message);
    }

    // Get current VM configuration
    const vmConfig = await proxmox.get(`/nodes/${node}/qemu/${vmid}/config`);

    // Delete VM
    await proxmox.delete(`/nodes/${node}/qemu/${vmid}`);

    // Wait a bit for cleanup
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Recreate VM with new template
    const newVMConfig = {
      vmid,
      node,
      name: service.serviceName,
      cores: service.specifications.cpu,
      memory: service.specifications.ram,
      net0: vmConfig.net0,
      scsi0: vmConfig.scsi0,
      ostype: 'l26',
      boot: 'c',
      bootdisk: 'scsi0',
      agent: 1,
      onboot: 1
    };

    await proxmox.post(`/nodes/${node}/qemu`, newVMConfig);

    // Set root password
    await proxmox.post(`/nodes/${node}/qemu/${vmid}/config`, {
      cipassword: service.credentials.password
    });

    // Start VM
    await proxmox.post(`/nodes/${node}/qemu/${vmid}/status/start`);
  }

  async reinstallCTInstance(proxmox, service, node, vmid, osTemplate) {
    // Stop CT if running
    try {
      await proxmox.post(`/nodes/${node}/lxc/${vmid}/status/stop`);
      // Wait for CT to stop
      await this.waitForVMStatus(proxmox, node, vmid, 'stopped', 'lxc');
    } catch (error) {
      console.log(`CT ${vmid} already stopped or error stopping:`, error.message);
    }

    // Get current CT configuration
    const ctConfig = await proxmox.get(`/nodes/${node}/lxc/${vmid}/config`);

    // Delete CT
    await proxmox.delete(`/nodes/${node}/lxc/${vmid}`);

    // Wait a bit for cleanup
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Get proxmox server details for storage info
    const proxmoxServer = await ProxmoxServer.findById(service.proxmoxServer);

    // Recreate CT with new template
    const newCTConfig = {
      vmid,
      node,
      hostname: service.serviceName,
      cores: service.specifications.cpu,
      memory: service.specifications.ram,
      net0: ctConfig.net0,
      rootfs: ctConfig.rootfs,
      ostemplate: `${proxmoxServer.storage.lxcTemplate}:vztmpl/${osTemplate}`,
      password: service.credentials.password,
      onboot: 1,
      start: 1
    };

    await proxmox.post(`/nodes/${node}/lxc`, newCTConfig);
  }

  async waitForVMStatus(proxmox, node, vmid, expectedStatus, type, maxWaitTime = 60000) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWaitTime) {
      try {
        const status = await proxmox.get(`/nodes/${node}/${type}/${vmid}/status/current`);
        if (status.status === expectedStatus) {
          return;
        }
      } catch (error) {
        // VM might not exist yet or other error
        if (expectedStatus === 'stopped' && error.message.includes('not found')) {
          return; // VM is deleted, which means it's stopped
        }
      }
      
      // Wait 2 seconds before checking again
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    throw new Error(`Timeout waiting for VM ${vmid} to reach status ${expectedStatus}`);
  }

  async getVMStatus(jobData) {
    const { serviceId, vmid, proxmoxServerId, node } = jobData;
    
    try {
      // Get service details
      const service = await Service.findById(serviceId);
      if (!service) {
        throw new Error('Service not found');
      }

      // Get Proxmox client
      const proxmox = await this.getProxmoxClient(proxmoxServerId);

      // Get VM/CT status
      let status;
      if (service.type === 'VM') {
        status = await proxmox.get(`/nodes/${node}/qemu/${vmid}/status/current`);
      } else {
        status = await proxmox.get(`/nodes/${node}/lxc/${vmid}/status/current`);
      }

      // Update service status if different
      if (service.proxmoxStatus !== status.status) {
        service.proxmoxStatus = status.status;
        await service.save();
      }

      return status;

    } catch (error) {
      console.error(`❌ Failed to get VM ${vmid} status:`, error);
      throw error;
    }
  }
}

module.exports = VPSControlService;
