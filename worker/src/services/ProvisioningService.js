const mongoose = require('mongoose');
const axios = require('axios');
const crypto = require('crypto');

// Simple Proxmox API client using axios
class ProxmoxClient {
  constructor(config) {
    this.host = config.host;
    this.port = config.port || 8006;
    this.username = config.username;
    this.password = config.password;
    this.realm = config.realm || 'pam';
    this.baseURL = `https://${this.host}:${this.port}/api2/json`;
    this.ticket = null;
    this.csrfToken = null;

    // Create axios instance with SSL verification disabled for self-signed certs
    this.axios = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      httpsAgent: new (require('https').Agent)({
        rejectUnauthorized: false
      })
    });
  }

  async authenticate() {
    try {
      const response = await this.axios.post('/access/ticket', {
        username: `${this.username}@${this.realm}`,
        password: this.password
      });

      this.ticket = response.data.data.ticket;
      this.csrfToken = response.data.data.CSRFPreventionToken;

      // Set default headers for authenticated requests
      this.axios.defaults.headers.common['Cookie'] = `PVEAuthCookie=${this.ticket}`;
      this.axios.defaults.headers.common['CSRFPreventionToken'] = this.csrfToken;

      return true;
    } catch (error) {
      console.error('Proxmox authentication failed:', error.message);
      throw new Error('Failed to authenticate with Proxmox server');
    }
  }

  async get(path) {
    if (!this.ticket) {
      await this.authenticate();
    }

    try {
      const response = await this.axios.get(path);
      return response.data;
    } catch (error) {
      if (error.response?.status === 401) {
        // Token expired, re-authenticate
        await this.authenticate();
        const response = await this.axios.get(path);
        return response.data;
      }
      throw error;
    }
  }

  async post(path, data = {}) {
    if (!this.ticket) {
      await this.authenticate();
    }

    try {
      const response = await this.axios.post(path, data);
      return response.data;
    } catch (error) {
      if (error.response?.status === 401) {
        // Token expired, re-authenticate
        await this.authenticate();
        const response = await this.axios.post(path, data);
        return response.data;
      }
      throw error;
    }
  }
}

// Import models
const Order = require('../models/Order');
const Service = require('../models/Service');
const Product = require('../models/Product');
const ProxmoxServer = require('../models/ProxmoxServer');
const User = require('../models/User');

class ProvisioningService {
  constructor() {
    this.proxmoxClients = new Map();
  }

  async getProxmoxClient(serverId) {
    if (this.proxmoxClients.has(serverId)) {
      return this.proxmoxClients.get(serverId);
    }

    const server = await ProxmoxServer.findById(serverId);
    if (!server) {
      throw new Error('Proxmox server not found');
    }

    const client = new ProxmoxClient({
      host: server.host,
      port: server.port,
      username: server.user,
      password: server.password,
      realm: server.realm
    });

    this.proxmoxClients.set(serverId, client);
    return client;
  }

  async provisionVPS(orderId) {
    console.log(`🔄 Starting VPS provisioning for order: ${orderId}`);

    try {
      // Get order details
      const order = await Order.findById(orderId)
        .populate('product')
        .populate('user');

      if (!order) {
        throw new Error('Order not found');
      }

      if (order.status !== 'paid') {
        throw new Error('Order is not paid');
      }

      // Start provisioning
      await order.startProvisioning();

      // Get product and proxmox server details
      const product = await Product.findById(order.product._id)
        .populate('proxmoxServer');

      if (!product) {
        throw new Error('Product not found');
      }

      const proxmoxServer = product.proxmoxServer;
      if (!proxmoxServer) {
        throw new Error('Proxmox server not found');
      }

      // Check server capacity
      if (product.type === 'VM' && !proxmoxServer.canCreateVM()) {
        throw new Error('VM limit reached on Proxmox server');
      }

      if (product.type === 'CT' && !proxmoxServer.canCreateCT()) {
        throw new Error('CT limit reached on Proxmox server');
      }

      // Get Proxmox client
      const proxmox = await this.getProxmoxClient(proxmoxServer._id);

      // Generate VM ID
      const vmid = await this.generateVMID(proxmox);

      // Allocate IP address
      const ipAddress = await this.allocateIPAddress(proxmoxServer, product.specifications.network);

      // Generate credentials
      const credentials = this.generateCredentials();

      // Create VM/CT based on type
      let vmConfig;
      if (product.type === 'VM') {
        vmConfig = await this.createVM(proxmox, {
          vmid,
          node: proxmoxServer.defaultNode,
          product,
          order,
          ipAddress,
          credentials,
          proxmoxServer
        });
      } else {
        vmConfig = await this.createCT(proxmox, {
          vmid,
          node: proxmoxServer.defaultNode,
          product,
          order,
          ipAddress,
          credentials,
          proxmoxServer
        });
      }

      // Update server statistics
      if (product.type === 'VM') {
        await proxmoxServer.incrementVMCount();
      } else {
        await proxmoxServer.incrementCTCount();
      }

      // Calculate next due date
      const nextDue = this.calculateNextDueDate(order.billing.cycle);

      // Create service record
      const service = new Service({
        user: order.user._id,
        product: order.product._id,
        proxmoxServer: proxmoxServer._id,
        order: order._id,
        serviceName: order.serviceName,
        vmid,
        node: proxmoxServer.defaultNode,
        type: product.type,
        specifications: product.specifications,
        network: {
          ipAddress,
          gateway: product.specifications.network === 'public' 
            ? proxmoxServer.network.public.gateway 
            : proxmoxServer.network.private.gateway,
          subnet: product.specifications.network === 'public'
            ? proxmoxServer.network.public.subnet
            : proxmoxServer.network.private.subnet,
          bridge: product.specifications.network === 'public'
            ? proxmoxServer.network.public.bridge
            : proxmoxServer.network.private.bridge
        },
        credentials,
        os: order.selectedOS,
        status: 'active',
        proxmoxStatus: 'running',
        billing: {
          cycle: order.billing.cycle,
          amount: order.billing.amount,
          currency: order.billing.currency,
          nextDue,
          autoRenew: true
        },
        reverseProxy: order.reverseProxy || {
          enabled: product.specifications.network === 'private',
          ports: [{ port: 22, enabled: true }]
        }
      });

      await service.save();

      // Complete order provisioning
      await order.completeProvisioning({
        vmid,
        ipAddress,
        credentials
      });

      // Decrease product stock if applicable
      if (product.stock > 0) {
        await product.decreaseStock();
      }

      console.log(`✅ VPS provisioning completed for order: ${orderId}`);
      console.log(`📋 Service details: VMID ${vmid}, IP ${ipAddress}`);

      return service;

    } catch (error) {
      console.error(`❌ VPS provisioning failed for order: ${orderId}`, error);

      // Mark order as failed
      const order = await Order.findById(orderId);
      if (order) {
        await order.failProvisioning(error.message);
      }

      throw error;
    }
  }

  async generateVMID(proxmox) {
    // Generate a random VMID between 1000-9999
    let vmid;
    let attempts = 0;
    const maxAttempts = 100;

    do {
      vmid = Math.floor(Math.random() * (9999 - 1000 + 1)) + 1000;
      attempts++;

      try {
        // Check if VMID is already in use
        await proxmox.get(`/cluster/resources?type=vm`);
        // If we get here, VMID is available
        break;
      } catch (error) {
        // VMID might be in use, try another
        if (attempts >= maxAttempts) {
          throw new Error('Unable to generate unique VMID');
        }
      }
    } while (attempts < maxAttempts);

    return vmid;
  }

  async allocateIPAddress(proxmoxServer, networkType) {
    // Simple IP allocation - in production, implement proper IP management
    const network = networkType === 'public' 
      ? proxmoxServer.network.public 
      : proxmoxServer.network.private;

    // Parse IP range (e.g., "*************-*************")
    const [startIP, endIP] = network.ipRange.split('-');
    const startParts = startIP.split('.').map(Number);
    const endParts = endIP.split('.').map(Number);

    // Generate random IP in range
    const lastOctet = Math.floor(Math.random() * (endParts[3] - startParts[3] + 1)) + startParts[3];
    const ipAddress = `${startParts[0]}.${startParts[1]}.${startParts[2]}.${lastOctet}`;

    return ipAddress;
  }

  generateCredentials() {
    const password = crypto.randomBytes(12).toString('base64').slice(0, 16);
    return {
      username: 'root',
      password,
      sshPort: 22
    };
  }

  calculateNextDueDate(billingCycle) {
    const now = new Date();
    const nextDue = new Date(now);

    switch (billingCycle) {
      case 'monthly':
        nextDue.setMonth(nextDue.getMonth() + 1);
        break;
      case 'quarterly':
        nextDue.setMonth(nextDue.getMonth() + 3);
        break;
      case 'annually':
        nextDue.setFullYear(nextDue.getFullYear() + 1);
        break;
      default:
        nextDue.setMonth(nextDue.getMonth() + 1);
    }

    return nextDue;
  }

  async createVM(proxmox, config) {
    const { vmid, node, product, order, ipAddress, credentials, proxmoxServer } = config;

    const vmConfig = {
      vmid,
      node,
      name: order.serviceName,
      cores: product.specifications.cpu,
      memory: product.specifications.ram,
      net0: `virtio,bridge=${proxmoxServer.network[product.specifications.network].bridge}`,
      scsi0: `${proxmoxServer.storage.default}:${product.specifications.disk}`,
      ostype: 'l26', // Linux
      boot: 'c',
      bootdisk: 'scsi0',
      agent: 1,
      onboot: 1
    };

    // Create VM
    await proxmox.post(`/nodes/${node}/qemu`, vmConfig);

    // Set root password
    await proxmox.post(`/nodes/${node}/qemu/${vmid}/config`, {
      cipassword: credentials.password
    });

    // Start VM
    await proxmox.post(`/nodes/${node}/qemu/${vmid}/status/start`);

    return vmConfig;
  }

  async createCT(proxmox, config) {
    const { vmid, node, product, order, ipAddress, credentials, proxmoxServer } = config;

    const ctConfig = {
      vmid,
      node,
      hostname: order.serviceName,
      cores: product.specifications.cpu,
      memory: product.specifications.ram,
      net0: `name=eth0,bridge=${proxmoxServer.network[product.specifications.network].bridge},ip=${ipAddress}/${proxmoxServer.network[product.specifications.network].subnet.split('/')[1]},gw=${proxmoxServer.network[product.specifications.network].gateway}`,
      rootfs: `${proxmoxServer.storage.default}:${product.specifications.disk}`,
      ostemplate: `${proxmoxServer.storage.lxcTemplate}:vztmpl/${order.selectedOS.template}`,
      password: credentials.password,
      onboot: 1,
      start: 1
    };

    // Create CT
    await proxmox.post(`/nodes/${node}/lxc`, ctConfig);

    return ctConfig;
  }
}

module.exports = ProvisioningService;
