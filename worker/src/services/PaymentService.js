const Order = require('../models/Order');

class PaymentService {
  constructor() {
    // Initialize Tripay client if needed
    this.tripayConfig = {
      merchantCode: process.env.TRIPAY_MERCHANT_CODE,
      apiKey: process.env.TRIPAY_API_KEY,
      privateKey: process.env.TRIPAY_PRIVATE_KEY,
      mode: process.env.TRIPAY_MODE || 'sandbox'
    };
  }

  async createPayment(orderId, tripayData) {
    try {
      console.log(`🔄 Creating payment for order: ${orderId}`);

      // Get order details
      const order = await Order.findById(orderId);
      if (!order) {
        throw new Error('Order not found');
      }

      // For now, we'll simulate payment creation
      // In production, integrate with actual Tripay API
      const paymentResponse = await this.simulatePaymentCreation(tripayData);

      // Update order with payment details
      order.payment.invoiceId = paymentResponse.reference;
      order.payment.paymentUrl = paymentResponse.checkout_url;
      order.payment.method = tripayData.method;
      order.payment.amount = tripayData.amount;
      order.payment.currency = 'IDR';

      await order.save();

      console.log(`✅ Payment created for order: ${orderId}`);
      console.log(`💳 Payment URL: ${paymentResponse.checkout_url}`);

      return paymentResponse;

    } catch (error) {
      console.error(`❌ Failed to create payment for order: ${orderId}`, error);
      throw error;
    }
  }

  async simulatePaymentCreation(tripayData) {
    // Simulate Tripay API response
    // In production, replace this with actual Tripay API call
    const reference = `TXN${Date.now()}${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
    
    return {
      success: true,
      message: 'Transaction created',
      data: {
        reference,
        merchant_ref: tripayData.merchant_ref,
        payment_selection_type: 'static',
        payment_method: tripayData.method,
        payment_name: 'QRIS',
        customer_name: tripayData.customer_name,
        customer_email: tripayData.customer_email,
        customer_phone: null,
        callback_url: null,
        return_url: tripayData.return_url,
        amount: tripayData.amount,
        fee_merchant: Math.floor(tripayData.amount * 0.007), // 0.7% fee
        fee_customer: 0,
        total_fee: Math.floor(tripayData.amount * 0.007),
        amount_received: tripayData.amount - Math.floor(tripayData.amount * 0.007),
        pay_code: null,
        pay_url: null,
        checkout_url: `https://tripay.co.id/checkout/${reference}`,
        order_items: tripayData.order_items,
        status: 'UNPAID',
        expired_time: tripayData.expired_time
      },
      checkout_url: `https://tripay.co.id/checkout/${reference}`,
      reference
    };
  }

  async verifyPayment(reference) {
    try {
      console.log(`🔄 Verifying payment: ${reference}`);

      // In production, call Tripay API to verify payment
      // For now, simulate verification
      const paymentStatus = await this.simulatePaymentVerification(reference);

      console.log(`✅ Payment verification completed: ${reference}`);
      return paymentStatus;

    } catch (error) {
      console.error(`❌ Failed to verify payment: ${reference}`, error);
      throw error;
    }
  }

  async simulatePaymentVerification(reference) {
    // Simulate payment verification
    // In production, replace with actual Tripay API call
    return {
      success: true,
      data: {
        reference,
        status: 'PAID',
        amount: 50000,
        amount_received: 49650,
        paid_at: Math.floor(Date.now() / 1000)
      }
    };
  }

  async processWebhook(webhookData) {
    try {
      console.log(`🔄 Processing payment webhook:`, webhookData.reference);

      // Find order by merchant reference
      const order = await Order.findOne({ orderNumber: webhookData.merchant_ref });
      if (!order) {
        throw new Error('Order not found for webhook');
      }

      // Process based on payment status
      switch (webhookData.status) {
        case 'PAID':
          await order.markAsPaid({
            transactionId: webhookData.reference,
            amount: webhookData.amount_received,
            method: webhookData.payment_method
          });
          
          // Add to provisioning queue
          const { getRedisClient } = require('../config/redis');
          const redis = getRedisClient();
          
          const provisioningJobData = {
            action: 'provision_vps',
            orderId: order._id
          };
          
          await redis.lpush('provisioning_queue', JSON.stringify(provisioningJobData));
          break;

        case 'EXPIRED':
        case 'FAILED':
          order.payment.status = 'failed';
          order.status = 'failed';
          await order.save();
          break;

        default:
          console.log(`ℹ️ Unhandled payment status: ${webhookData.status}`);
      }

      console.log(`✅ Webhook processed: ${webhookData.reference}`);

    } catch (error) {
      console.error(`❌ Failed to process webhook:`, error);
      throw error;
    }
  }

  async getPaymentMethods() {
    try {
      // In production, fetch from Tripay API
      // For now, return static list
      return [
        {
          group: 'Virtual Account',
          code: 'BRIVA',
          name: 'BRI Virtual Account',
          type: 'virtual_account',
          fee_merchant: {
            flat: 4000,
            percent: 0
          },
          fee_customer: {
            flat: 0,
            percent: 0
          },
          total_fee: {
            flat: 4000,
            percent: 0
          },
          minimum_fee: 0,
          maximum_fee: 0,
          icon_url: 'https://tripay.co.id/images/payment/bri.png',
          active: true
        },
        {
          group: 'E-Wallet',
          code: 'QRIS',
          name: 'QRIS',
          type: 'qris',
          fee_merchant: {
            flat: 0,
            percent: 0.7
          },
          fee_customer: {
            flat: 0,
            percent: 0
          },
          total_fee: {
            flat: 0,
            percent: 0.7
          },
          minimum_fee: 0,
          maximum_fee: 0,
          icon_url: 'https://tripay.co.id/images/payment/qris.png',
          active: true
        }
      ];

    } catch (error) {
      console.error('❌ Failed to get payment methods:', error);
      throw error;
    }
  }
}

module.exports = PaymentService;
