const axios = require('axios');
const https = require('https');
const ProxmoxServer = require('../models/ProxmoxServer');

// Custom Proxmox Client Implementation
class ProxmoxClient {
  constructor(config) {
    this.host = config.host;
    this.port = config.port || 8006;
    this.username = config.username;
    this.password = config.password;
    this.realm = config.realm || 'pam';
    this.ticket = null;
    this.csrfToken = null;

    // Create axios instance with SSL verification disabled
    this.axios = axios.create({
      baseURL: `https://${this.host}:${this.port}/api2/json`,
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      }),
      timeout: 30000
    });
  }

  async authenticate() {
    try {
      const response = await this.axios.post('/access/ticket', {
        username: `${this.username}@${this.realm}`,
        password: this.password
      });

      this.ticket = response.data.data.ticket;
      this.csrfToken = response.data.data.CSRFPreventionToken;

      // Set default headers for authenticated requests
      this.axios.defaults.headers.common['Cookie'] = `PVEAuthCookie=${this.ticket}`;
      this.axios.defaults.headers.common['CSRFPreventionToken'] = this.csrfToken;

      return true;
    } catch (error) {
      console.error('Proxmox authentication failed:', error.message);
      throw new Error('Failed to authenticate with Proxmox');
    }
  }

  async request(method, path, data = null) {
    // Authenticate if no ticket
    if (!this.ticket) {
      await this.authenticate();
    }

    try {
      const config = {
        method,
        url: path,
        data
      };

      const response = await this.axios(config);
      return response.data.data;
    } catch (error) {
      // If authentication error, try to re-authenticate once
      if (error.response && error.response.status === 401) {
        this.ticket = null;
        await this.authenticate();

        const config = {
          method,
          url: path,
          data
        };

        const response = await this.axios(config);
        return response.data.data;
      }

      throw error;
    }
  }

  async get(path) {
    return this.request('GET', path);
  }

  async post(path, data) {
    return this.request('POST', path, data);
  }

  async put(path, data) {
    return this.request('PUT', path, data);
  }

  async delete(path) {
    return this.request('DELETE', path);
  }
}

class ProxmoxService {
  constructor() {
    this.proxmoxClients = new Map();
  }

  async getProxmoxClient(serverId) {
    if (this.proxmoxClients.has(serverId)) {
      return this.proxmoxClients.get(serverId);
    }

    const server = await ProxmoxServer.findById(serverId);
    if (!server) {
      throw new Error('Proxmox server not found');
    }

    const client = new ProxmoxClient({
      host: server.host,
      port: server.port,
      username: server.user,
      password: server.password,
      realm: server.realm
    });

    this.proxmoxClients.set(serverId, client);
    return client;
  }

  async testConnection(jobData) {
    const { proxmoxServerId, host, port, user, password, realm } = jobData;

    try {
      console.log(`🔄 Testing connection to Proxmox server: ${host}:${port}`);

      // Create temporary client for testing
      const client = new ProxmoxAPI({
        host,
        port,
        username: user,
        password,
        realm
      });

      // Test connection by getting cluster status
      const clusterStatus = await client.get('/cluster/status');
      
      // Test getting nodes
      const nodes = await client.get('/nodes');

      // Update server health status
      const server = await ProxmoxServer.findById(proxmoxServerId);
      if (server) {
        server.healthStatus = 'healthy';
        server.lastHealthCheck = new Date();
        await server.save();
      }

      console.log(`✅ Connection test successful for: ${host}:${port}`);
      console.log(`📊 Found ${nodes.length} nodes`);

      return {
        success: true,
        clusterStatus,
        nodes: nodes.map(node => ({
          node: node.node,
          status: node.status,
          type: node.type,
          uptime: node.uptime
        }))
      };

    } catch (error) {
      console.error(`❌ Connection test failed for: ${host}:${port}`, error);

      // Update server health status
      const server = await ProxmoxServer.findById(proxmoxServerId);
      if (server) {
        server.healthStatus = 'unhealthy';
        server.lastHealthCheck = new Date();
        await server.save();
      }

      throw error;
    }
  }

  async getTemplates(jobData) {
    const { proxmoxServerId, responseKey } = jobData;

    try {
      console.log(`🔄 Fetching templates for Proxmox server: ${proxmoxServerId}`);

      const server = await ProxmoxServer.findById(proxmoxServerId);
      if (!server) {
        throw new Error('Proxmox server not found');
      }

      const proxmox = await this.getProxmoxClient(proxmoxServerId);

      // Get VM templates
      const vmTemplates = await this.getVMTemplates(proxmox, server.defaultNode);
      
      // Get CT templates
      const ctTemplates = await this.getCTTemplates(proxmox, server.defaultNode, server.storage.lxcTemplate);

      const templates = {
        vm: vmTemplates,
        ct: ctTemplates
      };

      // Store result in Redis for retrieval
      const { getRedisClient } = require('../config/redis');
      const redis = getRedisClient();
      await redis.setex(responseKey, 300, JSON.stringify(templates)); // 5 minutes TTL

      console.log(`✅ Templates fetched for server: ${proxmoxServerId}`);
      console.log(`📦 Found ${vmTemplates.length} VM templates and ${ctTemplates.length} CT templates`);

      return templates;

    } catch (error) {
      console.error(`❌ Failed to fetch templates for server: ${proxmoxServerId}`, error);
      throw error;
    }
  }

  async getVMTemplates(proxmox, node) {
    try {
      // Get all VMs and filter for templates
      const vms = await proxmox.get(`/nodes/${node}/qemu`);
      
      const templates = vms
        .filter(vm => vm.template === 1)
        .map(vm => ({
          vmid: vm.vmid,
          name: vm.name,
          status: vm.status,
          type: 'VM'
        }));

      return templates;

    } catch (error) {
      console.error('❌ Failed to get VM templates:', error);
      return [];
    }
  }

  async getCTTemplates(proxmox, node, storage) {
    try {
      // Get available CT templates from storage
      const templates = await proxmox.get(`/nodes/${node}/storage/${storage}/content?content=vztmpl`);
      
      const ctTemplates = templates.map(template => ({
        volid: template.volid,
        name: template.volid.split('/')[1].replace('.tar.gz', '').replace('.tar.xz', ''),
        size: template.size,
        type: 'CT'
      }));

      return ctTemplates;

    } catch (error) {
      console.error('❌ Failed to get CT templates:', error);
      return [];
    }
  }

  async getNodeResources(jobData) {
    const { proxmoxServerId, node } = jobData;

    try {
      console.log(`🔄 Fetching resources for node: ${node}`);

      const proxmox = await this.getProxmoxClient(proxmoxServerId);

      // Get node status
      const nodeStatus = await proxmox.get(`/nodes/${node}/status`);
      
      // Get node VMs
      const vms = await proxmox.get(`/nodes/${node}/qemu`);
      
      // Get node CTs
      const cts = await proxmox.get(`/nodes/${node}/lxc`);

      const resources = {
        node: node,
        status: nodeStatus,
        vms: vms.map(vm => ({
          vmid: vm.vmid,
          name: vm.name,
          status: vm.status,
          cpu: vm.cpu,
          mem: vm.mem,
          maxmem: vm.maxmem,
          disk: vm.disk,
          maxdisk: vm.maxdisk
        })),
        containers: cts.map(ct => ({
          vmid: ct.vmid,
          name: ct.name,
          status: ct.status,
          cpu: ct.cpu,
          mem: ct.mem,
          maxmem: ct.maxmem,
          disk: ct.disk,
          maxdisk: ct.maxdisk
        }))
      };

      console.log(`✅ Resources fetched for node: ${node}`);
      console.log(`📊 Found ${vms.length} VMs and ${cts.length} CTs`);

      return resources;

    } catch (error) {
      console.error(`❌ Failed to fetch resources for node: ${node}`, error);
      throw error;
    }
  }

  async updateServerStats(proxmoxServerId) {
    try {
      console.log(`🔄 Updating stats for Proxmox server: ${proxmoxServerId}`);

      const server = await ProxmoxServer.findById(proxmoxServerId);
      if (!server) {
        throw new Error('Proxmox server not found');
      }

      const proxmox = await this.getProxmoxClient(proxmoxServerId);

      // Get cluster resources
      const resources = await proxmox.get('/cluster/resources');

      // Count VMs and CTs
      const vms = resources.filter(r => r.type === 'qemu');
      const cts = resources.filter(r => r.type === 'lxc');

      // Update server stats
      server.stats.totalVMs = vms.length;
      server.stats.totalCTs = cts.length;
      server.lastHealthCheck = new Date();
      server.healthStatus = 'healthy';

      await server.save();

      console.log(`✅ Stats updated for server: ${proxmoxServerId}`);
      console.log(`📊 VMs: ${vms.length}, CTs: ${cts.length}`);

      return {
        totalVMs: vms.length,
        totalCTs: cts.length
      };

    } catch (error) {
      console.error(`❌ Failed to update stats for server: ${proxmoxServerId}`, error);
      
      // Mark server as unhealthy
      const server = await ProxmoxServer.findById(proxmoxServerId);
      if (server) {
        server.healthStatus = 'unhealthy';
        server.lastHealthCheck = new Date();
        await server.save();
      }

      throw error;
    }
  }

  async getClusterStatus(proxmoxServerId) {
    try {
      const proxmox = await this.getProxmoxClient(proxmoxServerId);
      
      const clusterStatus = await proxmox.get('/cluster/status');
      const nodes = await proxmox.get('/nodes');
      const resources = await proxmox.get('/cluster/resources');

      return {
        cluster: clusterStatus,
        nodes: nodes,
        resources: resources
      };

    } catch (error) {
      console.error(`❌ Failed to get cluster status:`, error);
      throw error;
    }
  }
}

module.exports = ProxmoxService;
