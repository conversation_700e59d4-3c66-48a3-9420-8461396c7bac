const mongoose = require('mongoose');

const serviceSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  product: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  proxmoxServer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ProxmoxServer',
    required: true
  },
  order: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order',
    required: true
  },
  serviceName: {
    type: String,
    required: true,
    trim: true
  },
  vmid: {
    type: Number,
    required: true
  },
  node: {
    type: String,
    required: true
  },
  type: {
    type: String,
    enum: ['VM', 'CT'],
    required: true
  },
  specifications: {
    cpu: Number,
    ram: Number,
    disk: Number,
    bandwidth: Number,
    network: {
      type: String,
      enum: ['public', 'private']
    }
  },
  network: {
    ipAddress: {
      type: String,
      required: true
    },
    gateway: String,
    subnet: String,
    bridge: String
  },
  credentials: {
    username: {
      type: String,
      default: 'root'
    },
    password: {
      type: String,
      required: true
    },
    sshPort: {
      type: Number,
      default: 22
    }
  },
  os: {
    name: String,
    template: String,
    version: String
  },
  status: {
    type: String,
    enum: ['pending', 'provisioning', 'active', 'suspended', 'terminated', 'error'],
    default: 'pending'
  },
  proxmoxStatus: {
    type: String,
    enum: ['running', 'stopped', 'suspended'],
    default: 'stopped'
  },
  billing: {
    cycle: {
      type: String,
      enum: ['monthly', 'quarterly', 'annually'],
      default: 'monthly'
    },
    amount: {
      type: Number,
      required: true
    },
    currency: {
      type: String,
      default: 'IDR'
    },
    nextDue: {
      type: Date,
      required: true
    },
    autoRenew: {
      type: Boolean,
      default: true
    }
  },
  reverseProxy: {
    enabled: {
      type: Boolean,
      default: false
    },
    domain: String,
    ssl: {
      type: Boolean,
      default: false
    },
    ports: [{
      port: Number,
      enabled: Boolean,
      proxyPort: Number
    }]
  },
  usage: {
    bandwidth: {
      used: {
        type: Number,
        default: 0
      },
      limit: Number
    },
    lastUpdated: Date
  },
  notes: String,
  suspensionReason: String,
  terminationDate: Date
}, {
  timestamps: true
});

// Indexes
serviceSchema.index({ user: 1, status: 1 });
serviceSchema.index({ vmid: 1, proxmoxServer: 1 }, { unique: true });
serviceSchema.index({ 'billing.nextDue': 1 });
serviceSchema.index({ status: 1 });

// Virtual for checking if service is active
serviceSchema.virtual('isActive').get(function() {
  return this.status === 'active';
});

// Virtual for checking if service is overdue
serviceSchema.virtual('isOverdue').get(function() {
  return this.billing.nextDue < new Date();
});

// Method to suspend service
serviceSchema.methods.suspend = function(reason) {
  this.status = 'suspended';
  this.suspensionReason = reason;
  return this.save();
};

// Method to activate service
serviceSchema.methods.activate = function() {
  this.status = 'active';
  this.suspensionReason = undefined;
  return this.save();
};

// Method to terminate service
serviceSchema.methods.terminate = function() {
  this.status = 'terminated';
  this.terminationDate = new Date();
  return this.save();
};

module.exports = mongoose.model('Service', serviceSchema);
