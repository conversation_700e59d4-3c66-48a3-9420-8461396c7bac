require('dotenv').config();
const mongoose = require('mongoose');
const Redis = require('ioredis');

// Import services
const ProvisioningService = require('./services/ProvisioningService');
const VPSControlService = require('./services/VPSControlService');
const PaymentService = require('./services/PaymentService');
const ProxmoxService = require('./services/ProxmoxService');

class NexbillingWorker {
  constructor() {
    this.redis = null;
    this.isRunning = false;
    this.queues = [
      'provisioning_queue',
      'vps_control_queue',
      'payment_queue',
      'proxmox_test_queue',
      'proxmox_info_queue'
    ];
  }

  async initialize() {
    try {
      // Connect to MongoDB
      await this.connectDatabase();
      
      // Connect to Redis
      await this.connectRedis();
      
      // Initialize services
      this.provisioningService = new ProvisioningService();
      this.vpsControlService = new VPSControlService();
      this.paymentService = new PaymentService();
      this.proxmoxService = new ProxmoxService();
      
      console.log('✅ Nexbilling Worker initialized successfully');
      
    } catch (error) {
      console.error('❌ Failed to initialize worker:', error);
      process.exit(1);
    }
  }

  async connectDatabase() {
    try {
      const mongoUri = process.env.MONGODB_URI || '******************************************************************************';
      
      await mongoose.connect(mongoUri, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
      });
      
      console.log('✅ Connected to MongoDB');
      
    } catch (error) {
      console.error('❌ Failed to connect to MongoDB:', error);
      throw error;
    }
  }

  async connectRedis() {
    try {
      const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
      
      this.redis = new Redis(redisUrl, {
        retryDelayOnFailover: 100,
        enableReadyCheck: false,
        maxRetriesPerRequest: null,
      });
      
      this.redis.on('connect', () => {
        console.log('✅ Connected to Redis');
      });
      
      this.redis.on('error', (err) => {
        console.error('❌ Redis connection error:', err);
      });
      
      // Test connection
      await this.redis.ping();
      console.log('🏓 Redis ping successful');
      
    } catch (error) {
      console.error('❌ Failed to connect to Redis:', error);
      throw error;
    }
  }

  async processJob(queueName, jobData) {
    try {
      console.log(`🔄 Processing job from ${queueName}:`, jobData.action);
      
      switch (queueName) {
        case 'provisioning_queue':
          await this.handleProvisioningJob(jobData);
          break;
          
        case 'vps_control_queue':
          await this.handleVPSControlJob(jobData);
          break;
          
        case 'payment_queue':
          await this.handlePaymentJob(jobData);
          break;
          
        case 'proxmox_test_queue':
          await this.handleProxmoxTestJob(jobData);
          break;
          
        case 'proxmox_info_queue':
          await this.handleProxmoxInfoJob(jobData);
          break;
          
        default:
          console.warn(`⚠️ Unknown queue: ${queueName}`);
      }
      
      console.log(`✅ Job completed: ${jobData.action}`);
      
    } catch (error) {
      console.error(`❌ Job failed: ${jobData.action}`, error);
      
      // Handle job failure (retry logic, error notifications, etc.)
      await this.handleJobFailure(queueName, jobData, error);
    }
  }

  async handleProvisioningJob(jobData) {
    switch (jobData.action) {
      case 'provision_vps':
        await this.provisioningService.provisionVPS(jobData.orderId);
        break;
      default:
        throw new Error(`Unknown provisioning action: ${jobData.action}`);
    }
  }

  async handleVPSControlJob(jobData) {
    switch (jobData.action) {
      case 'start_vm':
        await this.vpsControlService.startVM(jobData);
        break;
      case 'stop_vm':
        await this.vpsControlService.stopVM(jobData);
        break;
      case 'restart_vm':
        await this.vpsControlService.restartVM(jobData);
        break;
      case 'reinstall_vm':
        await this.vpsControlService.reinstallVM(jobData);
        break;
      default:
        throw new Error(`Unknown VPS control action: ${jobData.action}`);
    }
  }

  async handlePaymentJob(jobData) {
    switch (jobData.action) {
      case 'create_payment':
        await this.paymentService.createPayment(jobData.orderId, jobData.tripayData);
        break;
      default:
        throw new Error(`Unknown payment action: ${jobData.action}`);
    }
  }

  async handleProxmoxTestJob(jobData) {
    switch (jobData.action) {
      case 'test_connection':
        await this.proxmoxService.testConnection(jobData);
        break;
      default:
        throw new Error(`Unknown Proxmox test action: ${jobData.action}`);
    }
  }

  async handleProxmoxInfoJob(jobData) {
    switch (jobData.action) {
      case 'get_templates':
        await this.proxmoxService.getTemplates(jobData);
        break;
      default:
        throw new Error(`Unknown Proxmox info action: ${jobData.action}`);
    }
  }

  async handleJobFailure(queueName, jobData, error) {
    // Log error details
    console.error(`Job failure details:`, {
      queue: queueName,
      action: jobData.action,
      error: error.message,
      timestamp: new Date().toISOString()
    });

    // TODO: Implement retry logic, error notifications, etc.
    // For now, just log the error
  }

  async start() {
    this.isRunning = true;
    console.log('🚀 Nexbilling Worker started');
    console.log(`📋 Monitoring queues: ${this.queues.join(', ')}`);
    
    // Start processing each queue
    this.queues.forEach(queueName => {
      this.processQueue(queueName);
    });
  }

  async processQueue(queueName) {
    while (this.isRunning) {
      try {
        // Block and wait for jobs (BRPOP with 1 second timeout)
        const result = await this.redis.brpop(queueName, 1);
        
        if (result) {
          const [queue, jobDataString] = result;
          const jobData = JSON.parse(jobDataString);
          
          // Process the job
          await this.processJob(queueName, jobData);
        }
        
      } catch (error) {
        console.error(`❌ Error processing queue ${queueName}:`, error);
        
        // Wait a bit before retrying
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }
  }

  async stop() {
    console.log('🛑 Stopping Nexbilling Worker...');
    this.isRunning = false;
    
    if (this.redis) {
      await this.redis.quit();
    }
    
    await mongoose.connection.close();
    console.log('✅ Worker stopped successfully');
  }
}

// Initialize and start worker
const worker = new NexbillingWorker();

async function main() {
  try {
    await worker.initialize();
    await worker.start();
  } catch (error) {
    console.error('❌ Failed to start worker:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...');
  await worker.stop();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
  await worker.stop();
  process.exit(0);
});

// Start the worker
main();
