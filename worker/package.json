{"name": "nexbilling-worker", "version": "1.0.0", "description": "Nexbilling Worker Service for VPS Provisioning", "main": "src/worker.js", "scripts": {"start": "node src/worker.js", "dev": "nodemon src/worker.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["worker", "queue", "vps", "provisioning", "proxmox"], "author": "Nexbilling Team", "license": "MIT", "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "dotenv": "^16.3.1", "https": "^1.0.0", "ioredis": "^5.3.2", "mongoose": "^8.0.3", "nodemailer": "^6.9.7", "redis": "^4.6.11"}, "devDependencies": {"nodemon": "^3.0.2"}}