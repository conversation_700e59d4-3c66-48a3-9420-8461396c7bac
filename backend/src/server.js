require('dotenv').config();
const fastify = require('fastify')({ logger: true });
const mongoose = require('mongoose');
const Redis = require('ioredis');

// Import configurations
const { connectDatabase } = require('./config/database');
const { connectRedis } = require('./config/redis');

// Import routes
const authRoutes = require('./routes/auth');
const productRoutes = require('./routes/products');
const serviceRoutes = require('./routes/services');
const proxmoxRoutes = require('./routes/proxmox');
const paymentRoutes = require('./routes/payments');

// Register plugins
async function registerPlugins() {
  // CORS
  await fastify.register(require('@fastify/cors'), {
    origin: [
      'http://localhost:3001',
      'http://localhost:3000',
      process.env.FRONTEND_URL || 'http://localhost:3001'
    ],
    credentials: true
  });

  // JWT
  await fastify.register(require('@fastify/jwt'), {
    secret: process.env.JWT_SECRET || 'nexbilling-secret-key'
  });

  // Multipart (for file uploads)
  await fastify.register(require('@fastify/multipart'));

  // Static files
  await fastify.register(require('@fastify/static'), {
    root: require('path').join(__dirname, 'public'),
    prefix: '/public/'
  });
}

// Register routes
async function registerRoutes() {
  await fastify.register(authRoutes, { prefix: '/api/auth' });
  await fastify.register(productRoutes, { prefix: '/api/products' });
  await fastify.register(serviceRoutes, { prefix: '/api/services' });
  await fastify.register(proxmoxRoutes, { prefix: '/api/proxmox' });
  await fastify.register(paymentRoutes, { prefix: '/api/payments' });
}

// Health check route
fastify.get('/health', async (request, reply) => {
  return { 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'nexbilling-backend'
  };
});

// Root route
fastify.get('/', async (request, reply) => {
  return { 
    message: 'Nexbilling API Server',
    version: '1.0.0',
    docs: '/docs'
  };
});

// Start server
const start = async () => {
  try {
    // Connect to databases
    await connectDatabase();
    await connectRedis();
    
    // Register plugins and routes
    await registerPlugins();
    await registerRoutes();
    
    // Start the server
    const port = process.env.PORT || 3000;
    const host = process.env.HOST || '0.0.0.0';
    
    await fastify.listen({ port, host });
    
    console.log(`🚀 Nexbilling Backend Server running on http://${host}:${port}`);
    console.log(`📚 API Documentation: http://${host}:${port}/docs`);
    
  } catch (err) {
    fastify.log.error(err);
    process.exit(1);
  }
};

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Shutting down gracefully...');
  try {
    await fastify.close();
    await mongoose.connection.close();
    console.log('✅ Server closed successfully');
    process.exit(0);
  } catch (err) {
    console.error('❌ Error during shutdown:', err);
    process.exit(1);
  }
});

start();
