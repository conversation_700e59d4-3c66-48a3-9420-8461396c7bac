const Redis = require('ioredis');

let redisClient = null;

const connectRedis = async () => {
  try {
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
    
    redisClient = new Redis(redisUrl, {
      retryDelayOnFailover: 100,
      enableReadyCheck: false,
      maxRetriesPerRequest: null,
    });
    
    redisClient.on('connect', () => {
      console.log('✅ Connected to Redis');
    });
    
    redisClient.on('error', (err) => {
      console.error('❌ Redis connection error:', err);
    });
    
    redisClient.on('close', () => {
      console.log('⚠️ Redis connection closed');
    });
    
    redisClient.on('reconnecting', () => {
      console.log('🔄 Redis reconnecting...');
    });
    
    // Test connection
    await redisClient.ping();
    console.log('🏓 Redis ping successful');
    
  } catch (error) {
    console.error('❌ Failed to connect to Redis:', error);
    process.exit(1);
  }
};

const getRedisClient = () => {
  if (!redisClient) {
    throw new Error('Redis client not initialized. Call connectRedis() first.');
  }
  return redisClient;
};

module.exports = { 
  connectRedis, 
  getRedisClient 
};
