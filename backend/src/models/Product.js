const mongoose = require('mongoose');

const productSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  category: {
    type: String,
    enum: ['vps', 'dedicated', 'shared'],
    default: 'vps'
  },
  type: {
    type: String,
    enum: ['VM', 'CT'],
    required: true
  },
  specifications: {
    cpu: {
      type: Number,
      required: true,
      min: 1
    },
    ram: {
      type: Number,
      required: true,
      min: 512 // MB
    },
    disk: {
      type: Number,
      required: true,
      min: 10 // GB
    },
    bandwidth: {
      type: Number,
      required: true,
      min: 1 // GB
    },
    network: {
      type: String,
      enum: ['public', 'private'],
      required: true
    }
  },
  pricing: {
    monthly: {
      type: Number,
      required: true,
      min: 0
    },
    quarterly: {
      type: Number,
      min: 0
    },
    annually: {
      type: Number,
      min: 0
    },
    currency: {
      type: String,
      default: 'IDR'
    }
  },
  location: {
    type: String,
    required: true
  },
  proxmoxServer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ProxmoxServer',
    required: true
  },
  osTemplates: [{
    name: String,
    template: String,
    type: {
      type: String,
      enum: ['VM', 'CT']
    }
  }],
  reverseProxy: {
    enabled: {
      type: Boolean,
      default: false
    },
    domain: String,
    ssl: {
      type: Boolean,
      default: false
    },
    ports: [{
      port: Number,
      enabled: {
        type: Boolean,
        default: false
      }
    }]
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'maintenance'],
    default: 'active'
  },
  stock: {
    type: Number,
    default: -1 // -1 means unlimited
  },
  featured: {
    type: Boolean,
    default: false
  },
  sortOrder: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Indexes
productSchema.index({ category: 1, status: 1 });
productSchema.index({ 'specifications.network': 1 });
productSchema.index({ location: 1 });
productSchema.index({ featured: 1, sortOrder: 1 });

// Virtual for checking if product is available
productSchema.virtual('isAvailable').get(function() {
  return this.status === 'active' && (this.stock === -1 || this.stock > 0);
});

// Method to decrease stock
productSchema.methods.decreaseStock = function() {
  if (this.stock > 0) {
    this.stock -= 1;
  }
  return this.save();
};

// Method to increase stock
productSchema.methods.increaseStock = function() {
  if (this.stock !== -1) {
    this.stock += 1;
  }
  return this.save();
};

module.exports = mongoose.model('Product', productSchema);
