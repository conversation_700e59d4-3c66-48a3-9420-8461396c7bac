const mongoose = require('mongoose');

const orderSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  orderNumber: {
    type: String,
    required: true,
    unique: true
  },
  product: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  serviceName: {
    type: String,
    required: true,
    trim: true
  },
  selectedOS: {
    name: String,
    template: String,
    type: String
  },
  billing: {
    cycle: {
      type: String,
      enum: ['monthly', 'quarterly', 'annually'],
      required: true
    },
    amount: {
      type: Number,
      required: true,
      min: 0
    },
    currency: {
      type: String,
      default: 'IDR'
    }
  },
  payment: {
    invoiceId: String,
    method: String,
    status: {
      type: String,
      enum: ['pending', 'paid', 'failed', 'expired', 'cancelled'],
      default: 'pending'
    },
    paidAt: Date,
    amount: Number,
    currency: String,
    transactionId: String,
    paymentUrl: String
  },
  status: {
    type: String,
    enum: ['pending_payment', 'paid', 'provisioning', 'completed', 'failed', 'cancelled'],
    default: 'pending_payment'
  },
  provisioning: {
    status: {
      type: String,
      enum: ['pending', 'in_progress', 'completed', 'failed'],
      default: 'pending'
    },
    startedAt: Date,
    completedAt: Date,
    errorMessage: String,
    vmid: Number,
    ipAddress: String,
    credentials: {
      username: String,
      password: String
    }
  },
  reverseProxy: {
    enabled: {
      type: Boolean,
      default: false
    },
    domain: String,
    ssl: {
      type: Boolean,
      default: false
    },
    ports: [{
      port: {
        type: Number,
        default: 22
      },
      enabled: {
        type: Boolean,
        default: true
      }
    }]
  },
  notes: String,
  adminNotes: String
}, {
  timestamps: true
});

// Indexes
orderSchema.index({ user: 1, status: 1 });
orderSchema.index({ orderNumber: 1 });
orderSchema.index({ 'payment.invoiceId': 1 });
orderSchema.index({ 'payment.status': 1 });
orderSchema.index({ status: 1 });

// Pre-save middleware to generate order number
orderSchema.pre('save', async function(next) {
  if (this.isNew && !this.orderNumber) {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    this.orderNumber = `NXB-${timestamp}-${random}`;
  }
  next();
});

// Virtual for checking if order is paid
orderSchema.virtual('isPaid').get(function() {
  return this.payment.status === 'paid';
});

// Virtual for checking if order is completed
orderSchema.virtual('isCompleted').get(function() {
  return this.status === 'completed';
});

// Method to mark as paid
orderSchema.methods.markAsPaid = function(paymentData) {
  this.payment.status = 'paid';
  this.payment.paidAt = new Date();
  this.payment.transactionId = paymentData.transactionId;
  this.status = 'paid';
  return this.save();
};

// Method to start provisioning
orderSchema.methods.startProvisioning = function() {
  this.status = 'provisioning';
  this.provisioning.status = 'in_progress';
  this.provisioning.startedAt = new Date();
  return this.save();
};

// Method to complete provisioning
orderSchema.methods.completeProvisioning = function(provisioningData) {
  this.status = 'completed';
  this.provisioning.status = 'completed';
  this.provisioning.completedAt = new Date();
  this.provisioning.vmid = provisioningData.vmid;
  this.provisioning.ipAddress = provisioningData.ipAddress;
  this.provisioning.credentials = provisioningData.credentials;
  return this.save();
};

// Method to fail provisioning
orderSchema.methods.failProvisioning = function(errorMessage) {
  this.status = 'failed';
  this.provisioning.status = 'failed';
  this.provisioning.errorMessage = errorMessage;
  return this.save();
};

module.exports = mongoose.model('Order', orderSchema);
