const mongoose = require('mongoose');

const proxmoxServerSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  host: {
    type: String,
    required: true,
    trim: true
  },
  port: {
    type: Number,
    default: 8006
  },
  user: {
    type: String,
    required: true,
    trim: true
  },
  password: {
    type: String,
    required: true
  },
  realm: {
    type: String,
    default: 'pam'
  },
  limits: {
    vmLimit: {
      type: Number,
      default: 100
    },
    ctLimit: {
      type: Number,
      default: 100
    }
  },
  defaultNode: {
    type: String,
    required: true
  },
  storage: {
    default: {
      type: String,
      required: true
    },
    lxcTemplate: {
      type: String,
      required: true
    }
  },
  network: {
    public: {
      bridge: {
        type: String,
        required: true
      },
      ipRange: {
        type: String,
        required: true
      },
      subnet: {
        type: String,
        required: true
      },
      gateway: {
        type: String,
        required: true
      }
    },
    private: {
      bridge: {
        type: String,
        required: true
      },
      ipRange: {
        type: String,
        required: true
      },
      subnet: {
        type: String,
        required: true
      },
      gateway: {
        type: String,
        required: true
      }
    }
  },
  dns: {
    type: [String],
    default: ['*******', '*******']
  },
  proxy: {
    apiUrl: String,
    token: String
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'maintenance'],
    default: 'active'
  },
  lastHealthCheck: Date,
  healthStatus: {
    type: String,
    enum: ['healthy', 'unhealthy', 'unknown'],
    default: 'unknown'
  },
  stats: {
    totalVMs: {
      type: Number,
      default: 0
    },
    totalCTs: {
      type: Number,
      default: 0
    },
    usedIPs: {
      public: {
        type: Number,
        default: 0
      },
      private: {
        type: Number,
        default: 0
      }
    }
  }
}, {
  timestamps: true
});

// Indexes
proxmoxServerSchema.index({ status: 1 });
proxmoxServerSchema.index({ healthStatus: 1 });

// Virtual for checking if server is available
proxmoxServerSchema.virtual('isAvailable').get(function() {
  return this.status === 'active' && this.healthStatus === 'healthy';
});

// Method to check if VM limit is reached
proxmoxServerSchema.methods.canCreateVM = function() {
  return this.stats.totalVMs < this.limits.vmLimit;
};

// Method to check if CT limit is reached
proxmoxServerSchema.methods.canCreateCT = function() {
  return this.stats.totalCTs < this.limits.ctLimit;
};

// Method to increment VM count
proxmoxServerSchema.methods.incrementVMCount = function() {
  this.stats.totalVMs += 1;
  return this.save();
};

// Method to increment CT count
proxmoxServerSchema.methods.incrementCTCount = function() {
  this.stats.totalCTs += 1;
  return this.save();
};

// Method to decrement VM count
proxmoxServerSchema.methods.decrementVMCount = function() {
  if (this.stats.totalVMs > 0) {
    this.stats.totalVMs -= 1;
  }
  return this.save();
};

// Method to decrement CT count
proxmoxServerSchema.methods.decrementCTCount = function() {
  if (this.stats.totalCTs > 0) {
    this.stats.totalCTs -= 1;
  }
  return this.save();
};

module.exports = mongoose.model('ProxmoxServer', proxmoxServerSchema);
