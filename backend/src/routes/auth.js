const User = require('../models/User');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

async function authRoutes(fastify, options) {
  // Register
  fastify.post('/register', async (request, reply) => {
    try {
      const { username, email, password, firstName, lastName } = request.body;

      // Validation
      if (!username || !email || !password || !firstName || !lastName) {
        return reply.status(400).send({
          success: false,
          message: 'All fields are required'
        });
      }

      if (password.length < 6) {
        return reply.status(400).send({
          success: false,
          message: 'Password must be at least 6 characters long'
        });
      }

      // Check if user already exists
      const existingUser = await User.findOne({
        $or: [{ email }, { username }]
      });

      if (existingUser) {
        return reply.status(400).send({
          success: false,
          message: 'User with this email or username already exists'
        });
      }

      // Check if this is the first user (make them admin)
      const userCount = await User.countDocuments();
      const isFirstUser = userCount === 0;

      // Create new user
      const user = new User({
        username,
        email,
        password,
        firstName,
        lastName,
        role: isFirstUser ? 'admin' : 'user'
      });

      await user.save();

      // Generate JWT token
      const token = jwt.sign(
        { userId: user._id, role: user.role },
        process.env.JWT_SECRET || 'nexbilling-secret-key',
        { expiresIn: '7d' }
      );

      reply.send({
        success: true,
        message: 'User registered successfully',
        data: {
          user: user.toJSON(),
          token
        }
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Login
  fastify.post('/login', async (request, reply) => {
    try {
      const { login, password } = request.body;

      // Validation
      if (!login || !password) {
        return reply.status(400).send({
          success: false,
          message: 'Login and password are required'
        });
      }

      // Find user by email or username
      const user = await User.findOne({
        $or: [{ email: login }, { username: login }]
      });

      if (!user) {
        return reply.status(401).send({
          success: false,
          message: 'Invalid credentials'
        });
      }

      // Check password
      const isPasswordValid = await user.comparePassword(password);
      if (!isPasswordValid) {
        return reply.status(401).send({
          success: false,
          message: 'Invalid credentials'
        });
      }

      // Check if user is active
      if (user.status !== 'active') {
        return reply.status(401).send({
          success: false,
          message: 'Account is not active'
        });
      }

      // Update last login
      user.lastLogin = new Date();
      await user.save();

      // Generate JWT token
      const token = jwt.sign(
        { userId: user._id, role: user.role },
        process.env.JWT_SECRET || 'nexbilling-secret-key',
        { expiresIn: '7d' }
      );

      reply.send({
        success: true,
        message: 'Login successful',
        data: {
          user: user.toJSON(),
          token
        }
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Get current user profile
  fastify.get('/profile', {
    preHandler: async (request, reply) => {
      try {
        await request.jwtVerify();
      } catch (err) {
        reply.status(401).send({
          success: false,
          message: 'Unauthorized'
        });
      }
    }
  }, async (request, reply) => {
    try {
      const user = await User.findById(request.user.userId);
      if (!user) {
        return reply.status(404).send({
          success: false,
          message: 'User not found'
        });
      }

      reply.send({
        success: true,
        data: { user: user.toJSON() }
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Update profile
  fastify.put('/profile', {
    preHandler: async (request, reply) => {
      try {
        await request.jwtVerify();
      } catch (err) {
        reply.status(401).send({
          success: false,
          message: 'Unauthorized'
        });
      }
    }
  }, async (request, reply) => {
    try {
      const { firstName, lastName, profile } = request.body;
      
      const user = await User.findById(request.user.userId);
      if (!user) {
        return reply.status(404).send({
          success: false,
          message: 'User not found'
        });
      }

      // Update user data
      if (firstName) user.firstName = firstName;
      if (lastName) user.lastName = lastName;
      if (profile) {
        user.profile = { ...user.profile, ...profile };
      }

      await user.save();

      reply.send({
        success: true,
        message: 'Profile updated successfully',
        data: { user: user.toJSON() }
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Change password
  fastify.put('/change-password', {
    preHandler: async (request, reply) => {
      try {
        await request.jwtVerify();
      } catch (err) {
        reply.status(401).send({
          success: false,
          message: 'Unauthorized'
        });
      }
    }
  }, async (request, reply) => {
    try {
      const { currentPassword, newPassword } = request.body;

      if (!currentPassword || !newPassword) {
        return reply.status(400).send({
          success: false,
          message: 'Current password and new password are required'
        });
      }

      if (newPassword.length < 6) {
        return reply.status(400).send({
          success: false,
          message: 'New password must be at least 6 characters long'
        });
      }

      const user = await User.findById(request.user.userId);
      if (!user) {
        return reply.status(404).send({
          success: false,
          message: 'User not found'
        });
      }

      // Verify current password
      const isCurrentPasswordValid = await user.comparePassword(currentPassword);
      if (!isCurrentPasswordValid) {
        return reply.status(400).send({
          success: false,
          message: 'Current password is incorrect'
        });
      }

      // Update password
      user.password = newPassword;
      await user.save();

      reply.send({
        success: true,
        message: 'Password changed successfully'
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });
}

module.exports = authRoutes;
