const Product = require('../models/Product');
const ProxmoxServer = require('../models/ProxmoxServer');

async function productRoutes(fastify, options) {
  // Get all products (public)
  fastify.get('/', async (request, reply) => {
    try {
      const { category, type, location, network } = request.query;
      
      const filter = { status: 'active' };
      
      if (category) filter.category = category;
      if (type) filter.type = type;
      if (location) filter.location = location;
      if (network) filter['specifications.network'] = network;

      const products = await Product.find(filter)
        .populate('proxmoxServer', 'name location')
        .sort({ featured: -1, sortOrder: 1, createdAt: -1 });

      reply.send({
        success: true,
        data: { products }
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Get product by ID (public)
  fastify.get('/:id', async (request, reply) => {
    try {
      const product = await Product.findById(request.params.id)
        .populate('proxmoxServer', 'name location');

      if (!product) {
        return reply.status(404).send({
          success: false,
          message: 'Product not found'
        });
      }

      if (product.status !== 'active') {
        return reply.status(404).send({
          success: false,
          message: 'Product not available'
        });
      }

      reply.send({
        success: true,
        data: { product }
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Admin routes - require authentication and admin role
  const adminAuth = async (request, reply) => {
    try {
      await request.jwtVerify();
      if (request.user.role !== 'admin') {
        return reply.status(403).send({
          success: false,
          message: 'Admin access required'
        });
      }
    } catch (err) {
      reply.status(401).send({
        success: false,
        message: 'Unauthorized'
      });
    }
  };

  // Get all products (admin)
  fastify.get('/admin/all', { preHandler: adminAuth }, async (request, reply) => {
    try {
      const products = await Product.find()
        .populate('proxmoxServer', 'name location')
        .sort({ createdAt: -1 });

      reply.send({
        success: true,
        data: { products }
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Create product (admin)
  fastify.post('/admin', { preHandler: adminAuth }, async (request, reply) => {
    try {
      const productData = request.body;

      // Validate required fields
      const requiredFields = ['name', 'description', 'type', 'specifications', 'pricing', 'location', 'proxmoxServer'];
      for (const field of requiredFields) {
        if (!productData[field]) {
          return reply.status(400).send({
            success: false,
            message: `${field} is required`
          });
        }
      }

      // Validate proxmox server exists
      const proxmoxServer = await ProxmoxServer.findById(productData.proxmoxServer);
      if (!proxmoxServer) {
        return reply.status(400).send({
          success: false,
          message: 'Invalid Proxmox server'
        });
      }

      // Set default reverse proxy ports for private network
      if (productData.specifications.network === 'private') {
        if (!productData.reverseProxy) {
          productData.reverseProxy = {
            enabled: false,
            ports: [{ port: 22, enabled: true }]
          };
        }
      }

      const product = new Product(productData);
      await product.save();

      const savedProduct = await Product.findById(product._id)
        .populate('proxmoxServer', 'name location');

      reply.status(201).send({
        success: true,
        message: 'Product created successfully',
        data: { product: savedProduct }
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Update product (admin)
  fastify.put('/admin/:id', { preHandler: adminAuth }, async (request, reply) => {
    try {
      const productData = request.body;

      // Validate proxmox server if provided
      if (productData.proxmoxServer) {
        const proxmoxServer = await ProxmoxServer.findById(productData.proxmoxServer);
        if (!proxmoxServer) {
          return reply.status(400).send({
            success: false,
            message: 'Invalid Proxmox server'
          });
        }
      }

      const product = await Product.findByIdAndUpdate(
        request.params.id,
        productData,
        { new: true, runValidators: true }
      ).populate('proxmoxServer', 'name location');

      if (!product) {
        return reply.status(404).send({
          success: false,
          message: 'Product not found'
        });
      }

      reply.send({
        success: true,
        message: 'Product updated successfully',
        data: { product }
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Delete product (admin)
  fastify.delete('/admin/:id', { preHandler: adminAuth }, async (request, reply) => {
    try {
      const product = await Product.findByIdAndDelete(request.params.id);

      if (!product) {
        return reply.status(404).send({
          success: false,
          message: 'Product not found'
        });
      }

      reply.send({
        success: true,
        message: 'Product deleted successfully'
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Get product categories and locations (public)
  fastify.get('/meta/options', async (request, reply) => {
    try {
      const categories = await Product.distinct('category', { status: 'active' });
      const types = await Product.distinct('type', { status: 'active' });
      const locations = await Product.distinct('location', { status: 'active' });
      const networks = await Product.distinct('specifications.network', { status: 'active' });

      reply.send({
        success: true,
        data: {
          categories,
          types,
          locations,
          networks
        }
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });
}

module.exports = productRoutes;
