const ProxmoxServer = require('../models/ProxmoxServer');

async function proxmoxRoutes(fastify, options) {
  // Admin authentication middleware
  const requireAdmin = async (request, reply) => {
    try {
      await request.jwtVerify();
      if (request.user.role !== 'admin') {
        return reply.status(403).send({
          success: false,
          message: 'Admin access required'
        });
      }
    } catch (err) {
      reply.status(401).send({
        success: false,
        message: 'Unauthorized'
      });
    }
  };

  // Get all Proxmox servers
  fastify.get('/servers', { preHandler: requireAdmin }, async (request, reply) => {
    try {
      const servers = await ProxmoxServer.find().sort({ createdAt: -1 });

      // Remove sensitive data
      const sanitizedServers = servers.map(server => {
        const serverObj = server.toObject();
        delete serverObj.password;
        delete serverObj.proxy.token;
        return serverObj;
      });

      reply.send({
        success: true,
        data: { servers: sanitizedServers }
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Get Proxmox server by ID
  fastify.get('/servers/:id', { preHandler: requireAdmin }, async (request, reply) => {
    try {
      const server = await ProxmoxServer.findById(request.params.id);

      if (!server) {
        return reply.status(404).send({
          success: false,
          message: 'Proxmox server not found'
        });
      }

      // Remove sensitive data
      const serverObj = server.toObject();
      delete serverObj.password;
      delete serverObj.proxy.token;

      reply.send({
        success: true,
        data: { server: serverObj }
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Create new Proxmox server
  fastify.post('/servers', { preHandler: requireAdmin }, async (request, reply) => {
    try {
      const serverData = request.body;

      // Validate required fields
      const requiredFields = ['name', 'host', 'user', 'password', 'defaultNode', 'storage', 'network'];
      for (const field of requiredFields) {
        if (!serverData[field]) {
          return reply.status(400).send({
            success: false,
            message: `${field} is required`
          });
        }
      }

      // Check if server name already exists
      const existingServer = await ProxmoxServer.findOne({ name: serverData.name });
      if (existingServer) {
        return reply.status(400).send({
          success: false,
          message: 'Server with this name already exists'
        });
      }

      const server = new ProxmoxServer(serverData);
      await server.save();

      // Remove sensitive data from response
      const serverObj = server.toObject();
      delete serverObj.password;
      delete serverObj.proxy.token;

      reply.status(201).send({
        success: true,
        message: 'Proxmox server created successfully',
        data: { server: serverObj }
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Update Proxmox server
  fastify.put('/servers/:id', { preHandler: requireAdmin }, async (request, reply) => {
    try {
      const serverData = request.body;

      // Check if name is being changed and already exists
      if (serverData.name) {
        const existingServer = await ProxmoxServer.findOne({ 
          name: serverData.name,
          _id: { $ne: request.params.id }
        });
        if (existingServer) {
          return reply.status(400).send({
            success: false,
            message: 'Server with this name already exists'
          });
        }
      }

      const server = await ProxmoxServer.findByIdAndUpdate(
        request.params.id,
        serverData,
        { new: true, runValidators: true }
      );

      if (!server) {
        return reply.status(404).send({
          success: false,
          message: 'Proxmox server not found'
        });
      }

      // Remove sensitive data from response
      const serverObj = server.toObject();
      delete serverObj.password;
      delete serverObj.proxy.token;

      reply.send({
        success: true,
        message: 'Proxmox server updated successfully',
        data: { server: serverObj }
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Delete Proxmox server
  fastify.delete('/servers/:id', { preHandler: requireAdmin }, async (request, reply) => {
    try {
      const server = await ProxmoxServer.findByIdAndDelete(request.params.id);

      if (!server) {
        return reply.status(404).send({
          success: false,
          message: 'Proxmox server not found'
        });
      }

      reply.send({
        success: true,
        message: 'Proxmox server deleted successfully'
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Test Proxmox server connection
  fastify.post('/servers/:id/test', { preHandler: requireAdmin }, async (request, reply) => {
    try {
      const server = await ProxmoxServer.findById(request.params.id);

      if (!server) {
        return reply.status(404).send({
          success: false,
          message: 'Proxmox server not found'
        });
      }

      // Add to Redis queue for worker to test connection
      const { getRedisClient } = require('../config/redis');
      const redis = getRedisClient();
      
      const jobData = {
        action: 'test_connection',
        proxmoxServerId: server._id,
        host: server.host,
        port: server.port,
        user: server.user,
        password: server.password,
        realm: server.realm
      };

      await redis.lpush('proxmox_test_queue', JSON.stringify(jobData));

      reply.send({
        success: true,
        message: 'Connection test queued successfully'
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Get server statistics
  fastify.get('/servers/:id/stats', { preHandler: requireAdmin }, async (request, reply) => {
    try {
      const server = await ProxmoxServer.findById(request.params.id);

      if (!server) {
        return reply.status(404).send({
          success: false,
          message: 'Proxmox server not found'
        });
      }

      // Calculate usage percentages
      const vmUsagePercent = server.limits.vmLimit > 0 ? 
        (server.stats.totalVMs / server.limits.vmLimit) * 100 : 0;
      
      const ctUsagePercent = server.limits.ctLimit > 0 ? 
        (server.stats.totalCTs / server.limits.ctLimit) * 100 : 0;

      const stats = {
        ...server.stats,
        usage: {
          vm: {
            used: server.stats.totalVMs,
            limit: server.limits.vmLimit,
            percentage: Math.round(vmUsagePercent)
          },
          ct: {
            used: server.stats.totalCTs,
            limit: server.limits.ctLimit,
            percentage: Math.round(ctUsagePercent)
          }
        },
        healthStatus: server.healthStatus,
        lastHealthCheck: server.lastHealthCheck
      };

      reply.send({
        success: true,
        data: { stats }
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Get available OS templates for a server
  fastify.get('/servers/:id/templates', { preHandler: requireAdmin }, async (request, reply) => {
    try {
      const server = await ProxmoxServer.findById(request.params.id);

      if (!server) {
        return reply.status(404).send({
          success: false,
          message: 'Proxmox server not found'
        });
      }

      // Add to Redis queue for worker to fetch templates
      const { getRedisClient } = require('../config/redis');
      const redis = getRedisClient();
      
      const jobData = {
        action: 'get_templates',
        proxmoxServerId: server._id,
        responseKey: `templates_${server._id}_${Date.now()}`
      };

      await redis.lpush('proxmox_info_queue', JSON.stringify(jobData));

      reply.send({
        success: true,
        message: 'Template fetch queued successfully',
        data: { responseKey: jobData.responseKey }
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });
}

module.exports = proxmoxRoutes;
