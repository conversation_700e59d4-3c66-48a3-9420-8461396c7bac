const Service = require('../models/Service');
const Order = require('../models/Order');
const { getRedisClient } = require('../config/redis');

async function serviceRoutes(fastify, options) {
  // Authentication middleware
  const requireAuth = async (request, reply) => {
    try {
      await request.jwtVerify();
    } catch (err) {
      reply.status(401).send({
        success: false,
        message: 'Unauthorized'
      });
    }
  };

  // Admin authentication middleware
  const requireAdmin = async (request, reply) => {
    try {
      await request.jwtVerify();
      if (request.user.role !== 'admin') {
        return reply.status(403).send({
          success: false,
          message: 'Admin access required'
        });
      }
    } catch (err) {
      reply.status(401).send({
        success: false,
        message: 'Unauthorized'
      });
    }
  };

  // Get user's services
  fastify.get('/', { preHandler: requireAuth }, async (request, reply) => {
    try {
      const { status, page = 1, limit = 10 } = request.query;
      
      const filter = { user: request.user.userId };
      if (status) filter.status = status;

      const skip = (page - 1) * limit;
      
      const services = await Service.find(filter)
        .populate('product', 'name type specifications')
        .populate('proxmoxServer', 'name location')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit));

      const total = await Service.countDocuments(filter);

      reply.send({
        success: true,
        data: {
          services,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / limit)
          }
        }
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Get service by ID
  fastify.get('/:id', { preHandler: requireAuth }, async (request, reply) => {
    try {
      const service = await Service.findById(request.params.id)
        .populate('product', 'name type specifications')
        .populate('proxmoxServer', 'name location')
        .populate('user', 'username email firstName lastName');

      if (!service) {
        return reply.status(404).send({
          success: false,
          message: 'Service not found'
        });
      }

      // Check if user owns the service or is admin
      if (service.user._id.toString() !== request.user.userId && request.user.role !== 'admin') {
        return reply.status(403).send({
          success: false,
          message: 'Access denied'
        });
      }

      reply.send({
        success: true,
        data: { service }
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // VPS Control Actions
  fastify.post('/:id/start', { preHandler: requireAuth }, async (request, reply) => {
    try {
      const service = await Service.findById(request.params.id);

      if (!service) {
        return reply.status(404).send({
          success: false,
          message: 'Service not found'
        });
      }

      // Check if user owns the service
      if (service.user.toString() !== request.user.userId) {
        return reply.status(403).send({
          success: false,
          message: 'Access denied'
        });
      }

      // Check if service is active
      if (service.status !== 'active') {
        return reply.status(400).send({
          success: false,
          message: 'Service is not active'
        });
      }

      // Add to Redis queue for worker processing
      const redis = getRedisClient();
      const jobData = {
        action: 'start_vm',
        serviceId: service._id,
        vmid: service.vmid,
        proxmoxServerId: service.proxmoxServer,
        node: service.node
      };

      await redis.lpush('vps_control_queue', JSON.stringify(jobData));

      reply.send({
        success: true,
        message: 'Start command queued successfully'
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Stop VPS
  fastify.post('/:id/stop', { preHandler: requireAuth }, async (request, reply) => {
    try {
      const service = await Service.findById(request.params.id);

      if (!service) {
        return reply.status(404).send({
          success: false,
          message: 'Service not found'
        });
      }

      if (service.user.toString() !== request.user.userId) {
        return reply.status(403).send({
          success: false,
          message: 'Access denied'
        });
      }

      if (service.status !== 'active') {
        return reply.status(400).send({
          success: false,
          message: 'Service is not active'
        });
      }

      const redis = getRedisClient();
      const jobData = {
        action: 'stop_vm',
        serviceId: service._id,
        vmid: service.vmid,
        proxmoxServerId: service.proxmoxServer,
        node: service.node
      };

      await redis.lpush('vps_control_queue', JSON.stringify(jobData));

      reply.send({
        success: true,
        message: 'Stop command queued successfully'
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Restart VPS
  fastify.post('/:id/restart', { preHandler: requireAuth }, async (request, reply) => {
    try {
      const service = await Service.findById(request.params.id);

      if (!service) {
        return reply.status(404).send({
          success: false,
          message: 'Service not found'
        });
      }

      if (service.user.toString() !== request.user.userId) {
        return reply.status(403).send({
          success: false,
          message: 'Access denied'
        });
      }

      if (service.status !== 'active') {
        return reply.status(400).send({
          success: false,
          message: 'Service is not active'
        });
      }

      const redis = getRedisClient();
      const jobData = {
        action: 'restart_vm',
        serviceId: service._id,
        vmid: service.vmid,
        proxmoxServerId: service.proxmoxServer,
        node: service.node
      };

      await redis.lpush('vps_control_queue', JSON.stringify(jobData));

      reply.send({
        success: true,
        message: 'Restart command queued successfully'
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Reinstall OS
  fastify.post('/:id/reinstall', { preHandler: requireAuth }, async (request, reply) => {
    try {
      const { osTemplate } = request.body;
      
      if (!osTemplate) {
        return reply.status(400).send({
          success: false,
          message: 'OS template is required'
        });
      }

      const service = await Service.findById(request.params.id);

      if (!service) {
        return reply.status(404).send({
          success: false,
          message: 'Service not found'
        });
      }

      if (service.user.toString() !== request.user.userId) {
        return reply.status(403).send({
          success: false,
          message: 'Access denied'
        });
      }

      if (service.status !== 'active') {
        return reply.status(400).send({
          success: false,
          message: 'Service is not active'
        });
      }

      const redis = getRedisClient();
      const jobData = {
        action: 'reinstall_vm',
        serviceId: service._id,
        vmid: service.vmid,
        proxmoxServerId: service.proxmoxServer,
        node: service.node,
        osTemplate
      };

      await redis.lpush('vps_control_queue', JSON.stringify(jobData));

      reply.send({
        success: true,
        message: 'Reinstall command queued successfully'
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Admin: Get all services
  fastify.get('/admin/all', { preHandler: requireAdmin }, async (request, reply) => {
    try {
      const { status, page = 1, limit = 20 } = request.query;
      
      const filter = {};
      if (status) filter.status = status;

      const skip = (page - 1) * limit;
      
      const services = await Service.find(filter)
        .populate('user', 'username email firstName lastName')
        .populate('product', 'name type specifications')
        .populate('proxmoxServer', 'name location')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit));

      const total = await Service.countDocuments(filter);

      reply.send({
        success: true,
        data: {
          services,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / limit)
          }
        }
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });
}

module.exports = serviceRoutes;
