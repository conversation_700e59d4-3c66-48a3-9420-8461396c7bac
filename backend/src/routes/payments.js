const Order = require('../models/Order');
const Product = require('../models/Product');
const User = require('../models/User');
const { getRedisClient } = require('../config/redis');

async function paymentRoutes(fastify, options) {
  // Authentication middleware
  const requireAuth = async (request, reply) => {
    try {
      await request.jwtVerify();
    } catch (err) {
      reply.status(401).send({
        success: false,
        message: 'Unauthorized'
      });
    }
  };

  // Create order and payment
  fastify.post('/create-order', { preHandler: requireAuth }, async (request, reply) => {
    try {
      const { productId, serviceName, billingCycle, selectedOS, reverseProxy } = request.body;

      // Validate required fields
      if (!productId || !serviceName || !billingCycle || !selectedOS) {
        return reply.status(400).send({
          success: false,
          message: 'Product ID, service name, billing cycle, and OS selection are required'
        });
      }

      // Get product details
      const product = await Product.findById(productId).populate('proxmoxServer');
      if (!product || product.status !== 'active') {
        return reply.status(404).send({
          success: false,
          message: 'Product not found or not available'
        });
      }

      // Check if product is available
      if (!product.isAvailable) {
        return reply.status(400).send({
          success: false,
          message: 'Product is out of stock'
        });
      }

      // Calculate amount based on billing cycle
      let amount;
      switch (billingCycle) {
        case 'monthly':
          amount = product.pricing.monthly;
          break;
        case 'quarterly':
          amount = product.pricing.quarterly || (product.pricing.monthly * 3);
          break;
        case 'annually':
          amount = product.pricing.annually || (product.pricing.monthly * 12);
          break;
        default:
          return reply.status(400).send({
            success: false,
            message: 'Invalid billing cycle'
          });
      }

      // Validate OS template
      const validOS = product.osTemplates.find(os => 
        os.name === selectedOS.name && os.template === selectedOS.template
      );
      if (!validOS) {
        return reply.status(400).send({
          success: false,
          message: 'Invalid OS selection'
        });
      }

      // Create order
      const order = new Order({
        user: request.user.userId,
        product: productId,
        serviceName,
        selectedOS,
        billing: {
          cycle: billingCycle,
          amount,
          currency: product.pricing.currency
        },
        reverseProxy: reverseProxy || {
          enabled: product.specifications.network === 'private',
          ports: [{ port: 22, enabled: true }]
        }
      });

      await order.save();

      // Create payment invoice using Tripay
      const tripayData = {
        method: 'QRIS', // Default method, can be changed
        merchant_ref: order.orderNumber,
        amount: amount,
        customer_name: `${request.user.firstName} ${request.user.lastName}`,
        customer_email: request.user.email,
        order_items: [{
          sku: product._id.toString(),
          name: `${product.name} - ${billingCycle}`,
          price: amount,
          quantity: 1,
          product_url: `${process.env.FRONTEND_URL}/products/${product._id}`,
          image_url: `${process.env.FRONTEND_URL}/images/vps-icon.png`
        }],
        return_url: `${process.env.FRONTEND_URL}/orders/${order._id}`,
        expired_time: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours
        signature: '' // Will be calculated by tripay-node
      };

      // Add to Redis queue for payment processing
      const redis = getRedisClient();
      const paymentJobData = {
        action: 'create_payment',
        orderId: order._id,
        tripayData
      };

      await redis.lpush('payment_queue', JSON.stringify(paymentJobData));

      reply.status(201).send({
        success: true,
        message: 'Order created successfully',
        data: {
          order: {
            id: order._id,
            orderNumber: order.orderNumber,
            amount: order.billing.amount,
            currency: order.billing.currency,
            status: order.status
          }
        }
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Get order details
  fastify.get('/orders/:id', { preHandler: requireAuth }, async (request, reply) => {
    try {
      const order = await Order.findById(request.params.id)
        .populate('product', 'name type specifications pricing')
        .populate('user', 'username email firstName lastName');

      if (!order) {
        return reply.status(404).send({
          success: false,
          message: 'Order not found'
        });
      }

      // Check if user owns the order or is admin
      if (order.user._id.toString() !== request.user.userId && request.user.role !== 'admin') {
        return reply.status(403).send({
          success: false,
          message: 'Access denied'
        });
      }

      reply.send({
        success: true,
        data: { order }
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Get user's orders
  fastify.get('/orders', { preHandler: requireAuth }, async (request, reply) => {
    try {
      const { status, page = 1, limit = 10 } = request.query;
      
      const filter = { user: request.user.userId };
      if (status) filter.status = status;

      const skip = (page - 1) * limit;
      
      const orders = await Order.find(filter)
        .populate('product', 'name type specifications')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit));

      const total = await Order.countDocuments(filter);

      reply.send({
        success: true,
        data: {
          orders,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / limit)
          }
        }
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Tripay webhook endpoint
  fastify.post('/webhook/tripay', async (request, reply) => {
    try {
      const signature = request.headers['x-callback-signature'];
      const payload = request.body;

      // Verify signature (implement proper signature verification)
      // const expectedSignature = crypto.createHmac('sha256', process.env.TRIPAY_PRIVATE_KEY)
      //   .update(JSON.stringify(payload))
      //   .digest('hex');

      // if (signature !== expectedSignature) {
      //   return reply.status(400).send({ success: false, message: 'Invalid signature' });
      // }

      // Find order by merchant reference
      const order = await Order.findOne({ orderNumber: payload.merchant_ref });
      if (!order) {
        return reply.status(404).send({ success: false, message: 'Order not found' });
      }

      // Process payment status
      if (payload.status === 'PAID') {
        // Mark order as paid
        await order.markAsPaid({
          transactionId: payload.reference,
          amount: payload.amount_received,
          method: payload.payment_method
        });

        // Add to provisioning queue
        const redis = getRedisClient();
        const provisioningJobData = {
          action: 'provision_vps',
          orderId: order._id
        };

        await redis.lpush('provisioning_queue', JSON.stringify(provisioningJobData));

        fastify.log.info(`Order ${order.orderNumber} marked as paid and queued for provisioning`);
      }

      reply.send({ success: true });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Check payment status
  fastify.get('/orders/:id/payment-status', { preHandler: requireAuth }, async (request, reply) => {
    try {
      const order = await Order.findById(request.params.id);

      if (!order) {
        return reply.status(404).send({
          success: false,
          message: 'Order not found'
        });
      }

      if (order.user.toString() !== request.user.userId && request.user.role !== 'admin') {
        return reply.status(403).send({
          success: false,
          message: 'Access denied'
        });
      }

      reply.send({
        success: true,
        data: {
          paymentStatus: order.payment.status,
          orderStatus: order.status,
          paymentUrl: order.payment.paymentUrl,
          amount: order.billing.amount,
          currency: order.billing.currency
        }
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Cancel order
  fastify.post('/orders/:id/cancel', { preHandler: requireAuth }, async (request, reply) => {
    try {
      const order = await Order.findById(request.params.id);

      if (!order) {
        return reply.status(404).send({
          success: false,
          message: 'Order not found'
        });
      }

      if (order.user.toString() !== request.user.userId) {
        return reply.status(403).send({
          success: false,
          message: 'Access denied'
        });
      }

      if (order.status !== 'pending_payment') {
        return reply.status(400).send({
          success: false,
          message: 'Order cannot be cancelled'
        });
      }

      order.status = 'cancelled';
      order.payment.status = 'cancelled';
      await order.save();

      reply.send({
        success: true,
        message: 'Order cancelled successfully'
      });

    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });
}

module.exports = paymentRoutes;
