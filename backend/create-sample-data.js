const mongoose = require('mongoose');

// MongoDB connection
const mongoUri = '***************************************************************************************';

// Models
const ProxmoxServerSchema = new mongoose.Schema({
  name: { type: String, required: true, unique: true, trim: true },
  host: { type: String, required: true, trim: true },
  port: { type: Number, default: 8006 },
  user: { type: String, required: true, trim: true },
  password: { type: String, required: true },
  realm: { type: String, default: 'pam' },
  limits: {
    vmLimit: { type: Number, default: 100 },
    ctLimit: { type: Number, default: 100 }
  },
  defaultNode: { type: String, required: true },
  storage: {
    default: { type: String, required: true },
    lxcTemplate: { type: String, required: true }
  },
  network: {
    public: {
      bridge: { type: String, required: true },
      ipRange: { type: String, required: true },
      subnet: { type: String, required: true },
      gateway: { type: String, required: true }
    },
    private: {
      bridge: { type: String, required: true },
      ipRange: { type: String, required: true },
      subnet: { type: String, required: true },
      gateway: { type: String, required: true }
    }
  },
  dns: { type: [String], default: ['*******', '*******'] },
  proxy: {
    apiUrl: String,
    token: String
  },
  status: { type: String, enum: ['active', 'inactive', 'maintenance'], default: 'active' },
  lastHealthCheck: Date,
  healthStatus: { type: String, enum: ['healthy', 'unhealthy', 'unknown'], default: 'unknown' },
  stats: {
    totalVMs: { type: Number, default: 0 },
    totalCTs: { type: Number, default: 0 },
    usedMemory: { type: Number, default: 0 },
    totalMemory: { type: Number, default: 0 },
    usedStorage: { type: Number, default: 0 },
    totalStorage: { type: Number, default: 0 },
    cpuUsage: { type: Number, default: 0 }
  }
}, {
  timestamps: true
});

const ProductSchema = new mongoose.Schema({
  name: { type: String, required: true, trim: true },
  description: { type: String, required: true },
  type: { type: String, enum: ['vps', 'dedicated'], required: true },
  category: { type: String, required: true },
  specifications: {
    cpu: { type: Number, required: true },
    memory: { type: Number, required: true },
    storage: { type: Number, required: true },
    bandwidth: { type: Number, required: true },
    ipAddresses: { type: Number, default: 1 }
  },
  pricing: {
    monthly: { type: Number, required: true },
    quarterly: { type: Number },
    semiAnnually: { type: Number },
    annually: { type: Number }
  },
  features: [String],
  proxmoxTemplate: {
    osType: { type: String, enum: ['vm', 'lxc'], required: true },
    templateId: String,
    templateName: String
  },
  status: { type: String, enum: ['active', 'inactive'], default: 'active' },
  stock: { type: Number, default: -1 },
  setupFee: { type: Number, default: 0 }
}, {
  timestamps: true
});

const ProxmoxServer = mongoose.model('ProxmoxServer', ProxmoxServerSchema);
const Product = mongoose.model('Product', ProductSchema);

async function createSampleData() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB');

    // Create sample Proxmox server
    console.log('Creating sample Proxmox server...');
    const proxmoxServer = new ProxmoxServer({
      name: 'Proxmox Server 1',
      host: '***********00',
      port: 8006,
      user: 'root',
      password: 'proxmox123',
      realm: 'pam',
      defaultNode: 'pve',
      storage: {
        default: 'local-lvm',
        lxcTemplate: 'local'
      },
      network: {
        public: {
          bridge: 'vmbr0',
          ipRange: '***********00-*************',
          subnet: '***********/24',
          gateway: '***********'
        },
        private: {
          bridge: 'vmbr1',
          ipRange: '********00-**********',
          subnet: '10.0.0.0/24',
          gateway: '********'
        }
      },
      status: 'active'
    });

    try {
      await proxmoxServer.save();
      console.log('✅ Proxmox server created:', proxmoxServer.name);
    } catch (error) {
      if (error.code === 11000) {
        console.log('⚠️ Proxmox server already exists, skipping...');
      } else {
        throw error;
      }
    }

    // Create sample products
    console.log('Creating sample products...');
    const products = [
      {
        name: 'VPS Basic',
        description: 'Basic VPS package for small websites and applications',
        type: 'vps',
        category: 'basic',
        specifications: {
          cpu: 1,
          memory: 1024,
          storage: 20,
          bandwidth: 1000,
          ipAddresses: 1
        },
        pricing: {
          monthly: 50000,
          quarterly: 135000,
          semiAnnually: 270000,
          annually: 500000
        },
        features: ['SSD Storage', '1 IPv4', '24/7 Support', 'Root Access'],
        proxmoxTemplate: {
          osType: 'lxc',
          templateId: 'ubuntu-20.04',
          templateName: 'Ubuntu 20.04 LTS'
        },
        status: 'active'
      },
      {
        name: 'VPS Standard',
        description: 'Standard VPS package for medium applications',
        type: 'vps',
        category: 'standard',
        specifications: {
          cpu: 2,
          memory: 2048,
          storage: 40,
          bandwidth: 2000,
          ipAddresses: 1
        },
        pricing: {
          monthly: 100000,
          quarterly: 270000,
          semiAnnually: 540000,
          annually: 1000000
        },
        features: ['SSD Storage', '1 IPv4', '24/7 Support', 'Root Access', 'Weekly Backup'],
        proxmoxTemplate: {
          osType: 'vm',
          templateId: 'ubuntu-22.04',
          templateName: 'Ubuntu 22.04 LTS'
        },
        status: 'active'
      },
      {
        name: 'VPS Premium',
        description: 'Premium VPS package for high-performance applications',
        type: 'vps',
        category: 'premium',
        specifications: {
          cpu: 4,
          memory: 4096,
          storage: 80,
          bandwidth: 5000,
          ipAddresses: 2
        },
        pricing: {
          monthly: 200000,
          quarterly: 540000,
          semiAnnually: 1080000,
          annually: 2000000
        },
        features: ['NVMe SSD Storage', '2 IPv4', '24/7 Support', 'Root Access', 'Daily Backup', 'DDoS Protection'],
        proxmoxTemplate: {
          osType: 'vm',
          templateId: 'ubuntu-22.04',
          templateName: 'Ubuntu 22.04 LTS'
        },
        status: 'active'
      }
    ];

    for (const productData of products) {
      try {
        const product = new Product(productData);
        await product.save();
        console.log('✅ Product created:', product.name);
      } catch (error) {
        if (error.code === 11000) {
          console.log('⚠️ Product already exists:', productData.name);
        } else {
          throw error;
        }
      }
    }

    console.log('\n🎉 Sample data created successfully!');
    console.log('\n📊 Summary:');
    console.log('- 1 Proxmox Server');
    console.log('- 3 VPS Products (Basic, Standard, Premium)');
    
  } catch (error) {
    console.error('❌ Error creating sample data:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

createSampleData();
