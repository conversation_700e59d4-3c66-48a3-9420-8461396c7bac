{"name": "nexbilling-backend", "version": "1.0.0", "description": "Nexbilling Backend API with Fastify", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["fastify", "api", "vps", "billing", "proxmox"], "author": "Nexbilling Team", "license": "MIT", "dependencies": {"fastify": "^4.24.3", "@fastify/cors": "^8.4.0", "@fastify/jwt": "^7.2.4", "@fastify/multipart": "^8.0.0", "@fastify/static": "^6.12.0", "mongoose": "^8.0.3", "redis": "^4.6.11", "ioredis": "^5.3.2", "axios": "^1.6.2", "nodemailer": "^6.9.7", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2"}}