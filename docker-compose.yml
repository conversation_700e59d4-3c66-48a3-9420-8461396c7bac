version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: nexbilling-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: nexbilling
      MONGO_INITDB_ROOT_PASSWORD: nexbilling123
      MONGO_INITDB_DATABASE: nexbilling
    ports:
      - "27017:27017"
    volumes:
      - ./mongo/data:/data/db
      - ./mongo/init:/docker-entrypoint-initdb.d
    networks:
      - nexbilling-network

  # Redis Cache/Queue
  redis:
    image: redis:7.2-alpine
    container_name: nexbilling-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - ./redis/data:/data
    networks:
      - nexbilling-network
    command: redis-server --appendonly yes

  # Backend API (Fastify)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: nexbilling-backend
    restart: unless-stopped
    environment:
      NODE_ENV: development
      PORT: 3000
      MONGODB_URI: ****************************************************************************
      REDIS_URL: redis://redis:6379
      JWT_SECRET: nexbilling-jwt-secret-key-change-in-production
      TRIPAY_MERCHANT_CODE: ${TRIPAY_MERCHANT_CODE}
      TRIPAY_API_KEY: ${TRIPAY_API_KEY}
      TRIPAY_PRIVATE_KEY: ${TRIPAY_PRIVATE_KEY}
      TRIPAY_MODE: ${TRIPAY_MODE:-sandbox}
      EMAIL_HOST: ${EMAIL_HOST}
      EMAIL_PORT: ${EMAIL_PORT:-587}
      EMAIL_USER: ${EMAIL_USER}
      EMAIL_PASS: ${EMAIL_PASS}
      EMAIL_FROM: ${EMAIL_FROM}
    ports:
      - "3000:3000"
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      - mongodb
      - redis
    networks:
      - nexbilling-network
    command: npm run dev

  # Worker Service
  worker:
    build:
      context: ./worker
      dockerfile: Dockerfile
    container_name: nexbilling-worker
    restart: unless-stopped
    environment:
      NODE_ENV: development
      MONGODB_URI: ****************************************************************************
      REDIS_URL: redis://redis:6379
    volumes:
      - ./worker:/app
      - /app/node_modules
    depends_on:
      - mongodb
      - redis
    networks:
      - nexbilling-network
    command: npm run dev

  # Frontend (React)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: nexbilling-frontend
    restart: unless-stopped
    environment:
      REACT_APP_API_URL: http://localhost:3000/api
      REACT_APP_APP_NAME: Nexbilling
    ports:
      - "3001:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - nexbilling-network
    command: npm start

networks:
  nexbilling-network:
    driver: bridge

volumes:
  mongodb_data:
  redis_data:
