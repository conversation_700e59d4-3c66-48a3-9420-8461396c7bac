# Database Configuration
MONGODB_URI=******************************************************************************
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=nexbilling-jwt-secret-key-change-in-production

# Tripay Payment Gateway Configuration
TRIPAY_MERCHANT_CODE=your_merchant_code
TRIPAY_API_KEY=your_api_key
TRIPAY_PRIVATE_KEY=your_private_key
TRIPAY_MODE=sandbox

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
EMAIL_FROM=<EMAIL>

# Application Configuration
NODE_ENV=development
PORT=3000
FRONTEND_URL=http://localhost:3001
BACKEND_URL=http://localhost:3000

# Proxmox Configuration (will be stored in database)
# These are just examples for initial setup
DEFAULT_PROXMOX_HOST=your-proxmox-server.com
DEFAULT_PROXMOX_PORT=8006
DEFAULT_PROXMOX_USER=root@pam
DEFAULT_PROXMOX_PASS=your_proxmox_password
DEFAULT_PROXMOX_REALM=pam
