const mongoose = require('mongoose');

// MongoDB connection
const mongoUri = '***************************************************************************************';

async function fixProductsOSTemplates() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB');

    // Get Product model
    const Product = mongoose.model('Product');
    
    // Update all products to add osTemplates
    const osTemplates = [
      {
        name: 'Ubuntu 20.04 LTS',
        template: 'ubuntu-20.04',
        type: 'CT'
      },
      {
        name: 'Ubuntu 22.04 LTS',
        template: 'ubuntu-22.04',
        type: 'VM'
      },
      {
        name: 'CentOS 8',
        template: 'centos-8',
        type: 'CT'
      },
      {
        name: 'Debian 11',
        template: 'debian-11',
        type: 'CT'
      },
      {
        name: 'AlmaLinux 9',
        template: 'almalinux-9',
        type: 'VM'
      }
    ];

    // Get all products
    const products = await Product.find({});
    console.log(`Found ${products.length} products to update`);

    for (const product of products) {
      // Add proxmoxServer reference (first server)
      const ProxmoxServer = mongoose.model('ProxmoxServer');
      const firstServer = await ProxmoxServer.findOne({});
      
      if (firstServer) {
        product.proxmoxServer = firstServer._id;
      }
      
      // Add osTemplates
      product.osTemplates = osTemplates;
      
      await product.save();
      console.log(`✅ Updated product: ${product.name}`);
    }

    console.log('\n🎉 All products updated successfully!');
    
  } catch (error) {
    console.error('❌ Error updating products:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

fixProductsOSTemplates();
