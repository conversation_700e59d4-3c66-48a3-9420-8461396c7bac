import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    
    const message = error.response?.data?.message || error.message || 'An error occurred';
    return Promise.reject(new Error(message));
  }
);

interface LoginResponse {
  success: boolean;
  message: string;
  data: {
    token: string;
    user: {
      id: string;
      username: string;
      email: string;
      firstName: string;
      lastName: string;
      role: 'user' | 'admin';
      status: 'active' | 'inactive' | 'suspended';
      createdAt: string;
    };
  };
}

interface RegisterData {
  username: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}

interface RegisterResponse {
  success: boolean;
  message: string;
  data: {
    token: string;
    user: {
      id: string;
      username: string;
      email: string;
      firstName: string;
      lastName: string;
      role: 'user' | 'admin';
      status: 'active' | 'inactive' | 'suspended';
      createdAt: string;
    };
  };
}

interface ProfileResponse {
  success: boolean;
  data: {
    user: {
      id: string;
      username: string;
      email: string;
      firstName: string;
      lastName: string;
      role: 'user' | 'admin';
      status: 'active' | 'inactive' | 'suspended';
      createdAt: string;
    };
  };
}

export const authService = {
  setAuthToken: (token: string | null) => {
    if (token) {
      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    } else {
      delete api.defaults.headers.common['Authorization'];
    }
  },

  login: async (email: string, password: string) => {
    const response: LoginResponse = await api.post('/auth/login', {
      login: email,
      password,
    });

    if (!response.success) {
      throw new Error(response.message);
    }

    return {
      token: response.data.token,
      user: response.data.user,
    };
  },

  register: async (userData: RegisterData) => {
    const response: RegisterResponse = await api.post('/auth/register', userData);

    if (!response.success) {
      throw new Error(response.message);
    }

    return {
      token: response.data.token,
      user: response.data.user,
    };
  },

  getProfile: async () => {
    const response: ProfileResponse = await api.get('/auth/profile');

    if (!response.success) {
      throw new Error('Failed to get profile');
    }

    return response.data.user;
  },

  updateProfile: async (userData: any) => {
    const response: ProfileResponse = await api.put('/auth/profile', userData);

    if (!response.success) {
      throw new Error('Failed to update profile');
    }

    return response.data.user;
  },

  changePassword: async (currentPassword: string, newPassword: string) => {
    const response = await api.post('/auth/change-password', {
      currentPassword,
      newPassword,
    });

    if (!response.success) {
      throw new Error(response.message);
    }

    return response;
  },

  logout: () => {
    localStorage.removeItem('token');
    delete api.defaults.headers.common['Authorization'];
  },
};

export default api;
