import React from 'react';
import { useAuth } from '../contexts/AuthContext.tsx';
import {
  ServerIcon,
  CubeIcon,
  ShoppingCartIcon,
  CurrencyDollarIcon,
} from '@heroicons/react/24/outline';

const DashboardPage: React.FC = () => {
  const { user } = useAuth();

  const stats = [
    {
      name: 'Active Services',
      value: '3',
      icon: ServerIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      name: 'Available Products',
      value: '12',
      icon: CubeIcon,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      name: 'Pending Orders',
      value: '1',
      icon: ShoppingCartIcon,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
    },
    {
      name: 'Monthly Spending',
      value: 'Rp 150,000',
      icon: CurrencyDollarIcon,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
  ];

  const recentServices = [
    {
      id: 1,
      name: 'Web Server 1',
      type: 'VM',
      status: 'running',
      ip: '*************',
      created: '2024-01-15',
    },
    {
      id: 2,
      name: 'Database Server',
      type: 'CT',
      status: 'running',
      ip: '*************',
      created: '2024-01-10',
    },
    {
      id: 3,
      name: 'Development Server',
      type: 'VM',
      status: 'stopped',
      ip: '*************',
      created: '2024-01-05',
    },
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'running':
        return 'badge-success';
      case 'stopped':
        return 'badge-danger';
      case 'pending':
        return 'badge-warning';
      default:
        return 'badge-gray';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          Welcome back, {user?.firstName}!
        </h1>
        <p className="text-gray-600">
          Here's an overview of your VPS services and account activity.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <div key={stat.name} className="card">
              <div className="card-body">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                      <Icon className={`h-6 w-6 ${stat.color}`} />
                    </div>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">{stat.name}</p>
                    <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Recent Services */}
      <div className="card">
        <div className="card-header">
          <h2 className="text-lg font-medium text-gray-900">Recent Services</h2>
        </div>
        <div className="card-body p-0">
          <div className="overflow-hidden">
            <table className="table">
              <thead className="table-header">
                <tr>
                  <th className="table-header-cell">Service Name</th>
                  <th className="table-header-cell">Type</th>
                  <th className="table-header-cell">Status</th>
                  <th className="table-header-cell">IP Address</th>
                  <th className="table-header-cell">Created</th>
                </tr>
              </thead>
              <tbody className="table-body">
                {recentServices.map((service) => (
                  <tr key={service.id}>
                    <td className="table-cell">
                      <div className="font-medium text-gray-900">{service.name}</div>
                    </td>
                    <td className="table-cell">
                      <span className="badge-info">{service.type}</span>
                    </td>
                    <td className="table-cell">
                      <span className={`badge ${getStatusBadge(service.status)}`}>
                        {service.status}
                      </span>
                    </td>
                    <td className="table-cell">
                      <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                        {service.ip}
                      </code>
                    </td>
                    <td className="table-cell text-gray-500">
                      {new Date(service.created).toLocaleDateString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
        <div className="card">
          <div className="card-body text-center">
            <CubeIcon className="mx-auto h-12 w-12 text-primary-600 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Browse Products</h3>
            <p className="text-gray-600 mb-4">
              Explore our VPS packages and find the perfect solution for your needs.
            </p>
            <button className="btn-primary">View Products</button>
          </div>
        </div>

        <div className="card">
          <div className="card-body text-center">
            <ServerIcon className="mx-auto h-12 w-12 text-green-600 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Manage Services</h3>
            <p className="text-gray-600 mb-4">
              Control your VPS instances, monitor performance, and manage configurations.
            </p>
            <button className="btn-secondary">Manage Services</button>
          </div>
        </div>

        <div className="card">
          <div className="card-body text-center">
            <ShoppingCartIcon className="mx-auto h-12 w-12 text-purple-600 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Order History</h3>
            <p className="text-gray-600 mb-4">
              View your past orders, track payments, and download invoices.
            </p>
            <button className="btn-outline">View Orders</button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
