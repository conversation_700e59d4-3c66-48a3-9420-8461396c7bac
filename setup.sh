#!/bin/bash

# Nexbilling Setup Script
# This script sets up the development environment for Nexbilling

set -e

echo "🚀 Setting up Nexbilling development environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
check_docker() {
    print_status "Checking Docker installation..."
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Docker and Docker Compose are installed"
}

# Check if Node.js is installed
check_nodejs() {
    print_status "Checking Node.js installation..."
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js (version 18 or higher)."
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18 or higher is required. Current version: $(node --version)"
        exit 1
    fi
    
    print_success "Node.js $(node --version) is installed"
}

# Create environment file
setup_env() {
    print_status "Setting up environment file..."
    if [ ! -f .env ]; then
        cp .env.example .env
        print_success "Created .env file from .env.example"
        print_warning "Please edit .env file with your actual configuration values"
    else
        print_warning ".env file already exists, skipping..."
    fi
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    mkdir -p mongo/data mongo/init
    mkdir -p redis/data
    print_success "Created data directories"
}

# Setup backend
setup_backend() {
    print_status "Setting up backend..."
    cd backend
    
    if [ ! -f package.json ]; then
        npm init -y
        print_success "Initialized backend package.json"
    fi
    
    # Install backend dependencies
    print_status "Installing backend dependencies..."
    npm install fastify @fastify/cors @fastify/jwt @fastify/multipart @fastify/static
    npm install mongoose redis ioredis
    npm install tripay-node proxmox-api
    npm install nodemailer bcryptjs jsonwebtoken
    npm install dotenv
    npm install --save-dev nodemon
    
    print_success "Backend dependencies installed"
    cd ..
}

# Setup worker
setup_worker() {
    print_status "Setting up worker..."
    cd worker
    
    if [ ! -f package.json ]; then
        npm init -y
        print_success "Initialized worker package.json"
    fi
    
    # Install worker dependencies
    print_status "Installing worker dependencies..."
    npm install mongoose redis ioredis
    npm install proxmox-api
    npm install dotenv
    npm install --save-dev nodemon
    
    print_success "Worker dependencies installed"
    cd ..
}

# Setup frontend
setup_frontend() {
    print_status "Setting up frontend..."
    if [ ! -d "frontend/src" ]; then
        cd frontend
        npx create-react-app . --template typescript
        
        # Install additional frontend dependencies
        print_status "Installing additional frontend dependencies..."
        npm install @tailwindcss/forms @headlessui/react @heroicons/react
        npm install axios react-router-dom
        npm install --save-dev tailwindcss postcss autoprefixer
        
        # Initialize Tailwind CSS
        npx tailwindcss init -p
        
        print_success "Frontend setup completed"
        cd ..
    else
        print_warning "Frontend already initialized, skipping..."
    fi
}

# Set permissions
set_permissions() {
    print_status "Setting permissions..."
    chmod +x setup.sh
    chmod -R 755 mongo redis
    print_success "Permissions set"
}

# Main setup function
main() {
    echo "=================================================="
    echo "🏗️  Nexbilling Development Environment Setup"
    echo "=================================================="
    
    check_docker
    check_nodejs
    setup_env
    create_directories
    setup_backend
    setup_worker
    setup_frontend
    set_permissions
    
    echo ""
    echo "=================================================="
    print_success "Setup completed successfully! 🎉"
    echo "=================================================="
    echo ""
    echo "Next steps:"
    echo "1. Edit .env file with your configuration"
    echo "2. Run: docker-compose up -d mongodb redis"
    echo "3. Run: docker-compose up backend worker frontend"
    echo ""
    echo "Access the application:"
    echo "- Frontend: http://localhost:3001"
    echo "- Backend API: http://localhost:3000"
    echo "- MongoDB: localhost:27017"
    echo "- Redis: localhost:6379"
    echo ""
}

# Run main function
main "$@"
