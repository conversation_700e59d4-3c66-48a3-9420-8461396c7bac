# Perencanaan Pembuatan Aplikasi "Nexbilling"

## 1. <PERSON><PERSON><PERSON>n Umum Aplikasi

Nexbilling adalah panel web VPS yang dirancang untuk memfasilitasi penjualan dan pengelolaan VPS dengan backend Proxmox. Aplikasi ini akan mendukung dua peran pengguna utama: **Pengguna Akhir** (pembeli dan pengelola VPS mereka) dan **Administrator** (pengelola sistem, produk, dan layanan).

## 2. Struktur Direktori Proyek

```
nexbilling/
├── frontend/             # Aplikasi web antarmuka pengguna (React + Tailwind CSS)
├── backend/              # API backend utama (Fastify)
├── worker/               # Layanan worker untuk tugas-tugas latar belakang (misalnya, provisioning VPS)
├── mongo/                # Direktori untuk konfigurasi dan data MongoDB
├── redis/                # Direktori untuk konfigurasi dan data Redis
├── .env                  # File konfigurasi variabel lingkungan
├── docker-compose.yml    # Konfigurasi Docker Compose untuk orkestrasi layanan
└── setup.sh              # Script instalasi untuk komponen proyek
```

## 3. Fitur Inti dan Modul

*   **Auth (User & Admin):**
    *   Pendaftaran dan login pengguna.
    *   Manajemen peran (User, Admin).
    *   Sistem otentikasi berbasis token (misalnya, JWT).
    *   Fitur reset kata sandi.
*   **Product:**
    *   Manajemen paket VPS (CPU, RAM, Disk, Bandwidth, Lokasi, Type {VM, CT}, Network {Public, Private}).
    *   Konfigurasi harga dan siklus penagihan.
    *   Manajemen ketersediaan produk.
    *   Jika Menggunakan private ip tampilkan menu reverse proxy dan port forwarding (proxy: bool, Domain: string, SSL: bool, Port: number), default port 22 enabled
*   **Services:**
    *   Daftar VPS yang aktif dan riwayat layanan pengguna.
    *   Tampilan status VPS (Running, Stopped, Suspended).
    *   Operasi dasar VPS (Start, Stop, Restart, Reinstall OS).
    *   Detail informasi VPS (IP Address, Kredensial Login).
*   **Ticketing:**
    *   Sistem dukungan tiket untuk pengguna.
    *   Antarmuka manajemen tiket untuk administrator.
    *   Notifikasi status tiket.
*   **Email:**
    *   Pengiriman email transaksional (verifikasi pendaftaran, notifikasi layanan, pembaruan tiket, detail VPS).
    *   Integrasi dengan layanan pengiriman email (misalnya, Nodemailer).
*   **Payment Gateway (Tripay-Node):**
    *   Integrasi penuh dengan API Tripay untuk pemrosesan pembayaran.
    *   Pembuatan invoice otomatis.
    *   Penanganan webhook untuk konfirmasi pembayaran instan.
*   **Proxmox (Module: Proxmox-API - [https://github.com/UrielCh/proxmox-api](https://github.com/UrielCh/proxmox-api)):**
    *   Koneksi aman ke server Proxmox.
    *   Fungsionalitas provisioning VPS baru.
    *   Manajemen siklus hidup VM (start, stop, suspend, terminate).
    *   Pemantauan dasar sumber daya Proxmox.
    *   Manajemen konfigurasi server Proxmox yang disimpan di database, meliputi:
        *   `name`: Nama server Proxmox
        *   `host/ip`: Hostname atau IP address server Proxmox
        *   `port`: Port API Proxmox (biasanya 8006)
        *   `user`: Username API Proxmox
        *   `pass`: Password API Proxmox
        *   `realm`: Realm otentikasi Proxmox (misalnya, `pam`, `pve`)
        *   `vm limit`: Batasan jumlah VM per server
        *   `ct limit`: Batasan jumlah CT per server
        *   `default node`: Node Proxmox default untuk provisioning
        *   `storage`: Nama storage default untuk VM/CT
        *   `lxc template storage`: Nama storage untuk template LXC
        *   `public network bridge`: Bridge jaringan publik di Proxmox
        *   `public ip range`: Rentang IP publik yang tersedia
        *   `public subnet`: Subnet publik
        *   `public gateway`: Gateway publik
        *   `private network bridge`: Bridge jaringan private di Proxmox
        *   `private ip range`: Rentang IP private yang tersedia
        *   `private subnet`: Subnet private
        *   `private gateway`: Gateway private
        *   `dns`: Server DNS untuk VM
        *   `proxy api url`: URL API proxy (jika digunakan)
        *   `proxy token`: Token otentikasi untuk API proxy (jika digunakan)

## 4. Teknologi yang Digunakan

*   **Backend Framework:** Fastify (Node.js)
*   **Database:** MongoDB
*   **Cache/Queue:** Redis
*   **Integrasi Pembayaran:** `tripay-node`
*   **Integrasi Virtualisasi:** `proxmox-api`
*   **Orkestrasi Kontainer:** Docker Compose
*   **Proxy VM:** Debian dengan Nginx, Certbot, Iptables-persistent, FastAPI
*   **Frontend:** React.js dengan Tailwind CSS
*   **Frontend Theme:** Materially Free React Admin Template ([https://themewagon.github.io/materially-free-react-admin-template/](https://themewagon.github.io/materially-free-react-admin-template/))

## 5. Arsitektur Sistem

```mermaid
graph TD
    A[Pengguna/Admin] -->|Mengakses| B[Frontend (Web Panel)]
    B -->|Permintaan API (HTTP/HTTPS)| C[Backend (Fastify API)]

    C -->|Otentikasi/Otorisasi| D[Database MongoDB]
    C -->|Manajemen Data (Produk, Layanan, Tiket, Konfigurasi Proxmox)| D

    C -->|Tugas Asinkron (mis. Provisioning)| E[Worker Service]
    E -->|Interaksi API| F[Proxmox API]
    F --> G[Server Proxmox]

    C -->|Notifikasi/Verifikasi| H[Layanan Email]
    C -->|Pembayaran| I[Tripay Payment Gateway]

    C -->|Caching/Queueing| J[Redis]
    E -->|Caching/Queueing| J

    subgraph Infrastruktur
        D -- Database --> K[MongoDB Container]
        J -- Cache/Queue --> L[Redis Container]
        K & L -- Dikelola oleh --> M[Docker Compose]
    end

    subgraph Jaringan Proxmox
        G -- Private IP --> N[Proxy API VM (Debian)]
        N -- Public IP --> C
        N -- Public IP --> B
        N -- Public IP --> I
        N -- Public IP --> H
    end

    N -- Menggunakan --> O[Nginx]
    N -- Menggunakan --> P[Certbot]
    N -- Menggunakan --> Q[Iptables-persistent]
```

## 6. Alur Kerja Utama (Contoh: Pembelian dan Provisioning VPS)

```mermaid
sequenceDiagram
    actor User
    participant Frontend
    participant Backend
    participant MongoDB
    participant Tripay
    participant Worker
    participant Proxmox
    participant ProxyAPI

    User->>Frontend: Pilih Paket VPS & Konfigurasi
    Frontend->>ProxyAPI: Permintaan API (ke Backend)
    ProxyAPI->>Backend: Teruskan Permintaan
    Backend->>MongoDB: Simpan Pesanan (Status: Pending Payment)
    Backend->>Tripay: Buat Invoice Pembayaran
    Tripay-->>Backend: Detail Invoice Pembayaran
    Backend->>MongoDB: Update Pesanan dengan Invoice ID
    Backend-->>ProxyAPI: Tampilkan Invoice & Instruksi Pembayaran
    ProxyAPI-->>Frontend: Tampilkan Invoice & Instruksi Pembayaran
    User->>Tripay: Lakukan Pembayaran
    Tripay-->>Backend: Notifikasi Pembayaran (Webhook)
    Backend->>MongoDB: Verifikasi Pembayaran, Update Pesanan (Status: Paid)
    Backend->>Worker: Kirim Tugas Provisioning VPS (via Redis Queue)
    Worker->>MongoDB: Ambil Konfigurasi Proxmox Server
    Worker->>Proxmox: Buat VM Baru (Proxmox API)
    Proxmox-->>Worker: Konfirmasi VM Dibuat & Detail
    Worker->>MongoDB: Simpan Detail VPS, Update Layanan Pengguna (Status: Active)
    Worker->>Backend: Notifikasi VPS Aktif
    Backend->>ProxyAPI: Notifikasi VPS Aktif
    ProxyAPI->>Frontend: Tampilkan VPS Aktif di Dashboard Pengguna
    Backend->>User: Kirim Email Detail Akses VPS
```

## 7. Tahapan Pengembangan (Garis Besar)

1.  **Inisialisasi Proyek:**
    *   Buat struktur direktori dasar.
    *   Konfigurasi `docker-compose.yml` untuk MongoDB, Redis, Backend, Worker, dan Frontend.
    *   Setup repositori Git.
    *   Buat script instalasi `./setup.sh` yang dapat menginstal dependensi dan menyiapkan lingkungan untuk `frontend`, `backend`, `db`, dan `redis`.
2.  **Pengembangan Backend (Fastify):**
    *   Setup Fastify dan koneksi MongoDB.
    *   Implementasi modul Auth (registrasi, login, otorisasi peran).
    *   Pengembangan modul Product (CRUD produk VPS).
    *   Pengembangan modul Services (manajemen layanan VPS pengguna).
    *   Pengembangan modul untuk manajemen konfigurasi server Proxmox di database.
3.  **Integrasi Proxmox:**
    *   Implementasi koneksi dan operasi dasar menggunakan `proxmox-api`.
    *   Fungsionalitas provisioning VM, start/stop/restart VM.
4.  **Integrasi Pembayaran (Tripay):**
    *   Implementasi `tripay-node` untuk pembuatan invoice.
    *   Pengembangan endpoint webhook untuk menerima notifikasi pembayaran.
5.  **Pengembangan Worker Service:**
    *   Setup Redis sebagai message queue.
    *   Implementasi logika worker untuk memproses tugas provisioning VPS secara asinkron, termasuk mengambil konfigurasi Proxmox dari database.
6.  **Pengembangan Frontend:**
    *   Implementasi frontend menggunakan React.js dan Tailwind CSS.
    *   Adaptasi dan integrasi "Materially Free React Admin Template".
    *   Desain antarmuka pengguna (UI/UX) untuk pengguna dan admin.
    *   Integrasi dengan API backend untuk semua fitur.
7.  **Fitur Tambahan:**
    *   Implementasi sistem Ticketing.
    *   Integrasi modul Email untuk notifikasi.
    *   Pengembangan dashboard pengguna dan admin yang komprehensif.
8.  **Setup Proxy API VM:**
    *   Instalasi Debian pada VM terpisah.
    *   Konfigurasi Nginx sebagai reverse proxy untuk backend dan frontend.
    *   Setup Certbot untuk SSL/TLS.
    *   Konfigurasi Iptables-persistent untuk aturan firewall dan perutean ke IP private Proxmox.
9.  **Pengujian dan Deployment:**
    *   Melakukan unit testing, integration testing, dan end-to-end testing.
    *   Persiapan skrip deployment untuk lingkungan produksi.